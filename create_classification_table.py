"""
Create QS WUR University Classification Table
Author: Dr. <PERSON><PERSON>, Symbiosis International (Deemed University)

Based on comprehensive research report analysis of QS methodology and institutional characteristics.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import pandas as pd
from matplotlib.patches import FancyBboxPatch

def create_classification_table():
    """Create comprehensive university classification table based on QS WUR methodology."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'primary': '#1e40af',
        'secondary': '#3b82f6',
        'accent': '#10b981',
        'warning': '#f59e0b',
        'danger': '#ef4444',
        'comprehensive': '#8b5cf6',
        'focused': '#06b6d4',
        'research': '#dc2626'
    }
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('QS WUR University Classification Framework: Institutional Characteristics & Impact', 
                 fontsize=20, fontweight='bold', y=0.96, color=colors['primary'])
    
    # 1. Institutional Type Classification
    ax1.set_title('Institutional Type Classification (QS Implicit Framework)', 
                  fontsize=16, fontweight='bold', color=colors['primary'])
    
    # Classification data based on research report
    classifications = [
        {
            'type': 'Comprehensive Research Universities',
            'description': 'Broad disciplinary coverage with high research intensity',
            'examples': 'MIT, Harvard, Oxford, Cambridge',
            'qs_advantage': 'High - Favored by methodology',
            'color': colors['comprehensive']
        },
        {
            'type': 'Specialized/Technical Universities', 
            'description': 'Focused on specific disciplines (Engineering, Medicine)',
            'examples': 'IIT Delhi, Caltech, Imperial College',
            'qs_advantage': 'Medium - Limited by scope',
            'color': colors['focused']
        },
        {
            'type': 'Multidisciplinary Universities',
            'description': 'Multiple schools/faculties with expanding scope',
            'examples': 'SIU, University of Toronto, NUS',
            'qs_advantage': 'Medium-High - Growing alignment',
            'color': colors['accent']
        },
        {
            'type': 'Liberal Arts Colleges',
            'description': 'Focus on undergraduate education and humanities',
            'examples': 'Williams College, Amherst College',
            'qs_advantage': 'Low - Methodology limitations',
            'color': colors['warning']
        },
        {
            'type': 'Professional/Business Schools',
            'description': 'Specialized in professional education',
            'examples': 'INSEAD, London Business School',
            'qs_advantage': 'Medium - Subject rankings better',
            'color': colors['secondary']
        }
    ]
    
    y_positions = np.linspace(0.9, 0.1, len(classifications))
    
    for i, cls in enumerate(classifications):
        y_pos = y_positions[i]
        
        # Type box
        type_box = FancyBboxPatch((0.02, y_pos - 0.06), 0.96, 0.1,
                                 boxstyle="round,pad=0.02", 
                                 facecolor=cls['color'], 
                                 alpha=0.2, edgecolor=cls['color'], linewidth=2)
        ax1.add_patch(type_box)
        
        # Type name
        ax1.text(0.05, y_pos + 0.02, cls['type'], ha='left', va='center', 
                fontsize=12, fontweight='bold', color=cls['color'], transform=ax1.transAxes)
        
        # Description
        ax1.text(0.05, y_pos - 0.02, cls['description'], ha='left', va='center', 
                fontsize=10, fontweight='normal', color='black', transform=ax1.transAxes)
        
        # QS Advantage
        ax1.text(0.75, y_pos, cls['qs_advantage'], ha='left', va='center', 
                fontsize=10, fontweight='bold', color='black', transform=ax1.transAxes,
                bbox=dict(boxstyle="round,pad=0.2", facecolor='lightgray', alpha=0.7))
    
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    
    # 2. Size-Based Classification
    ax2.set_title('Size-Based Classification & QS Impact', fontsize=16, fontweight='bold', color=colors['primary'])
    
    # Size categories with student numbers and QS implications
    size_data = {
        'Category': ['Very Large\n(>40,000)', 'Large\n(20,000-40,000)', 'Medium\n(10,000-20,000)', 
                    'Small\n(5,000-10,000)', 'Very Small\n(<5,000)'],
        'Students': [45000, 30000, 15000, 7500, 3000],
        'QS_Impact': ['High research volume\nbut diluted ratios', 'Balanced scale\nfor most indicators', 
                     'Optimal for QS\nmethodology', 'Good ratios but\nlimited research scale', 
                     'Excellent ratios\nbut minimal research'],
        'Examples': ['University of Central Florida', 'University of Toronto', 'SIU, NUS Singapore', 
                    'Dartmouth College', 'Caltech, Williams College']
    }
    
    # Create horizontal bar chart for size visualization
    y_pos = np.arange(len(size_data['Category']))
    bars = ax2.barh(y_pos, size_data['Students'], color=[colors['danger'], colors['warning'], 
                                                        colors['accent'], colors['secondary'], colors['primary']], 
                   alpha=0.8, edgecolor='black', linewidth=1)
    
    # Add student numbers
    for bar, students in zip(bars, size_data['Students']):
        ax2.text(bar.get_width() + 1000, bar.get_y() + bar.get_height()/2, 
                f'{students:,}', va='center', fontweight='bold', fontsize=10)
    
    ax2.set_yticks(y_pos)
    ax2.set_yticklabels(size_data['Category'], fontsize=11)
    ax2.set_xlabel('Student Population', fontsize=12, fontweight='bold')
    ax2.grid(True, alpha=0.3, axis='x')
    
    # Add QS impact annotations
    for i, impact in enumerate(size_data['QS_Impact']):
        ax2.text(25000, i, impact, va='center', ha='center', fontsize=9, 
                fontweight='normal', color='black',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    # 3. Research Intensity Classification
    ax3.set_title('Research Intensity Classification (Carnegie-Style)', fontsize=16, fontweight='bold', color=colors['primary'])
    
    research_categories = [
        {'level': 'R1: Very High Research', 'description': 'Extensive doctoral programs, very high research activity', 
         'qs_score': 85, 'examples': 'Harvard, MIT, SIU (expanding)', 'color': colors['research']},
        {'level': 'R2: High Research', 'description': 'Many doctoral programs, high research activity', 
         'qs_score': 70, 'examples': 'Regional research universities', 'color': colors['danger']},
        {'level': 'R3: Moderate Research', 'description': 'Some doctoral programs, moderate research', 
         'qs_score': 50, 'examples': 'Comprehensive universities', 'color': colors['warning']},
        {'level': 'M1: Master\'s Universities', 'description': 'Master\'s programs, limited research', 
         'qs_score': 35, 'examples': 'Regional master\'s universities', 'color': colors['secondary']},
        {'level': 'B: Baccalaureate Colleges', 'description': 'Primarily undergraduate, minimal research', 
         'qs_score': 20, 'examples': 'Liberal arts colleges', 'color': colors['primary']}
    ]
    
    # Research intensity visualization
    levels = [cat['level'] for cat in research_categories]
    scores = [cat['qs_score'] for cat in research_categories]
    colors_research = [cat['color'] for cat in research_categories]
    
    bars_research = ax3.bar(range(len(levels)), scores, color=colors_research, alpha=0.8, 
                           edgecolor='black', linewidth=1)
    
    # Add score labels
    for bar, score in zip(bars_research, scores):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2, 
                f'{score}', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    ax3.set_xticks(range(len(levels)))
    ax3.set_xticklabels([level.split(':')[0] for level in levels], fontsize=10)
    ax3.set_ylabel('Typical QS Performance Score', fontsize=12, fontweight='bold')
    ax3.grid(True, alpha=0.3, axis='y')
    ax3.set_ylim(0, 100)
    
    # 4. SIU's Classification Profile
    ax4.text(0.5, 0.95, 'SIU\'s Classification Profile & Strategic Position', ha='center', va='center', 
            fontsize=16, fontweight='bold', transform=ax4.transAxes, color=colors['primary'])
    
    # SIU's current profile
    siu_profile = [
        {'aspect': 'Institutional Type', 'current': 'Multidisciplinary University', 
         'target': 'Comprehensive Research University', 'timeline': '2025-2028'},
        {'aspect': 'Size Category', 'current': 'Medium (15,000+ students)', 
         'target': 'Large (20,000+ students)', 'timeline': '2026-2027'},
        {'aspect': 'Research Intensity', 'current': 'R1: Very High Research', 
         'target': 'Enhanced R1 with global impact', 'timeline': '2025-2026'},
        {'aspect': 'Academic Focus', 'current': 'Multi-school (Law, Management, Media, Liberal Arts, Medicine)', 
         'target': 'Comprehensive with research excellence', 'timeline': '2025-2028'},
        {'aspect': 'QS Advantage Level', 'current': 'Medium-High (Rank 696)', 
         'target': 'High (Target Rank 500)', 'timeline': '2028'}
    ]
    
    for i, profile in enumerate(siu_profile):
        y_pos = 0.8 - (i * 0.15)
        
        # Profile box
        profile_box = FancyBboxPatch((0.02, y_pos - 0.06), 0.96, 0.1,
                                    boxstyle="round,pad=0.02", 
                                    facecolor=colors['accent'], 
                                    alpha=0.1, edgecolor=colors['accent'], linewidth=1)
        ax4.add_patch(profile_box)
        
        # Aspect
        ax4.text(0.05, y_pos + 0.02, profile['aspect'], ha='left', va='center', 
                fontsize=11, fontweight='bold', color=colors['primary'], transform=ax4.transAxes)
        
        # Current status
        ax4.text(0.05, y_pos - 0.01, f"Current: {profile['current']}", ha='left', va='center', 
                fontsize=9, fontweight='normal', color='black', transform=ax4.transAxes)
        
        # Target
        ax4.text(0.05, y_pos - 0.04, f"Target: {profile['target']}", ha='left', va='center', 
                fontsize=9, fontweight='bold', color=colors['accent'], transform=ax4.transAxes)
        
        # Timeline
        ax4.text(0.85, y_pos, profile['timeline'], ha='center', va='center', 
                fontsize=9, fontweight='bold', color='black', transform=ax4.transAxes,
                bbox=dict(boxstyle="round,pad=0.2", facecolor='lightyellow', alpha=0.8))
    
    # Key insight
    ax4.text(0.5, 0.05, 'KEY: SIU is transitioning from Multidisciplinary to Comprehensive Research University\nwith strong alignment to QS methodology preferences', 
            ha='center', va='center', fontsize=11, fontweight='bold', 
            transform=ax4.transAxes, color=colors['primary'],
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
    
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_University_Classification_Table.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_detailed_classification_matrix():
    """Create detailed classification matrix with all characteristics."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'primary': '#1e40af',
        'secondary': '#3b82f6',
        'accent': '#10b981',
        'warning': '#f59e0b',
        'excellent': '#059669',
        'poor': '#dc2626'
    }
    
    fig, ax = plt.subplots(figsize=(18, 12))
    
    ax.text(0.5, 0.97, 'Detailed University Classification Matrix: QS WUR Performance Indicators', 
            ha='center', va='top', fontsize=18, fontweight='bold', 
            transform=ax.transAxes, color=colors['primary'])
    
    # Classification matrix data
    matrix_data = [
        ['Classification', 'Size Range', 'Research Level', 'Academic Breadth', 'QS Advantage', 'Typical Rank Range', 'Examples'],
        ['Comprehensive Research', '20,000-50,000', 'R1: Very High', 'Very Broad', 'Excellent', '1-200', 'Harvard, MIT, Oxford'],
        ['Large Research', '15,000-40,000', 'R1: Very High', 'Broad', 'High', '50-400', 'University of Toronto, NUS'],
        ['Multidisciplinary', '10,000-25,000', 'R1/R2: High', 'Multiple Schools', 'Medium-High', '300-800', 'SIU, Regional Universities'],
        ['Specialized Technical', '5,000-20,000', 'R1: Very High', 'Narrow/Deep', 'Medium', '100-500', 'IIT Delhi, Caltech'],
        ['Professional Schools', '2,000-10,000', 'R2: High', 'Professional Focus', 'Medium', '200-600', 'Business Schools, Medical'],
        ['Liberal Arts', '1,000-5,000', 'R3: Moderate', 'Undergraduate Focus', 'Low', '400-1000+', 'Williams, Amherst'],
        ['Regional Universities', '8,000-20,000', 'R2/R3: Moderate', 'Regional Focus', 'Low-Medium', '500-1200+', 'State Universities'],
        ['Emerging Universities', '5,000-15,000', 'R2: High', 'Expanding', 'Medium', '600-1500+', 'New Private Universities']
    ]
    
    # Create table
    cell_height = 0.08
    cell_width = 0.14
    start_y = 0.85
    start_x = 0.02
    
    # Color coding for QS Advantage
    advantage_colors = {
        'Excellent': colors['excellent'],
        'High': colors['accent'],
        'Medium-High': colors['secondary'],
        'Medium': colors['warning'],
        'Low-Medium': '#fbbf24',
        'Low': colors['poor']
    }
    
    for i, row in enumerate(matrix_data):
        y_pos = start_y - (i * cell_height)
        
        for j, cell in enumerate(row):
            x_pos = start_x + (j * cell_width)
            
            # Header row styling
            if i == 0:
                cell_color = colors['primary']
                text_color = 'white'
                font_weight = 'bold'
                font_size = 10
            else:
                # Color code QS Advantage column
                if j == 4 and cell in advantage_colors:
                    cell_color = advantage_colors[cell]
                    text_color = 'white' if cell in ['Excellent', 'Low'] else 'black'
                else:
                    cell_color = 'lightgray' if i % 2 == 0 else 'white'
                    text_color = 'black'
                font_weight = 'bold' if j == 0 and i > 0 else 'normal'
                font_size = 9
            
            # Cell rectangle
            cell_rect = FancyBboxPatch((x_pos, y_pos - cell_height/2), cell_width, cell_height,
                                      boxstyle="round,pad=0.005", 
                                      facecolor=cell_color, 
                                      alpha=0.8, edgecolor='black', linewidth=0.5)
            ax.add_patch(cell_rect)
            
            # Cell text
            ax.text(x_pos + cell_width/2, y_pos, cell, ha='center', va='center', 
                   fontsize=font_size, fontweight=font_weight, color=text_color, 
                   transform=ax.transAxes, wrap=True)
    
    # Legend for QS Advantage colors
    legend_y = 0.15
    ax.text(0.02, legend_y, 'QS Advantage Legend:', ha='left', va='center', 
           fontsize=12, fontweight='bold', color=colors['primary'], transform=ax.transAxes)
    
    legend_items = list(advantage_colors.items())
    for i, (level, color) in enumerate(legend_items):
        x_pos = 0.02 + (i * 0.12)
        
        # Legend color box
        legend_box = FancyBboxPatch((x_pos, legend_y - 0.05), 0.1, 0.03,
                                   boxstyle="round,pad=0.005", 
                                   facecolor=color, 
                                   alpha=0.8, edgecolor='black', linewidth=0.5)
        ax.add_patch(legend_box)
        
        # Legend text
        text_color = 'white' if level in ['Excellent', 'Low'] else 'black'
        ax.text(x_pos + 0.05, legend_y - 0.035, level, ha='center', va='center', 
               fontsize=8, fontweight='bold', color=text_color, transform=ax.transAxes)
    
    # SIU highlight
    ax.text(0.5, 0.05, 'SIU Classification: Multidisciplinary University transitioning to Comprehensive Research\nCurrent Rank: 696 | Target Classification: Large Research University (Rank 400-600)', 
           ha='center', va='center', fontsize=12, fontweight='bold', 
           transform=ax.transAxes, color=colors['primary'],
           bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Detailed_Classification_Matrix.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Creating comprehensive university classification tables...")
    
    # Create main classification table
    create_classification_table()
    print("✓ Main classification table created: QS_WUR_2026_University_Classification_Table.png")
    
    # Create detailed classification matrix
    create_detailed_classification_matrix()
    print("✓ Detailed classification matrix created: QS_WUR_2026_Detailed_Classification_Matrix.png")
    
    print("\nUniversity classification visualizations created:")
    print("• Comprehensive institutional type classification")
    print("• Size-based categories with QS impact analysis")
    print("• Research intensity classification (Carnegie-style)")
    print("• SIU's current profile and strategic positioning")
    print("• Detailed classification matrix with performance indicators")
    print("\n🎯 Complete classification framework ready for Directors Meeting!")
