"""
QS Institution Classifications - PowerPoint Presentation Generator
Author: Dr<PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-27

This script creates a comprehensive PowerPoint presentation for QS Institution Classifications
with professional formatting and embedded visualizations.
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import os

def create_qs_classifications_presentation():
    """Create comprehensive PowerPoint presentation for QS Institution Classifications."""
    
    # Create presentation
    prs = Presentation()
    
    # Define professional color scheme
    colors = {
        'primary': RGBColor(46, 134, 171),      # Professional blue
        'secondary': RGBColor(162, 59, 114),    # Deep magenta
        'accent': RGBColor(241, 143, 1),        # Orange
        'success': RGBColor(199, 62, 29),       # Red
        'dark': RGBColor(33, 37, 41),           # Dark gray
        'light': RGBColor(248, 249, 250)        # Light gray
    }
    
    # Slide 1: Title Slide
    slide_layout = prs.slide_layouts[0]  # Title slide layout
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "QS Institution Classifications"
    title.text_frame.paragraphs[0].font.size = Pt(44)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = colors['primary']
    
    subtitle.text = ("Comprehensive Framework for University Categorization\n\n"
                    "Four Classification Dimensions:\n"
                    "• Size (Student Body)\n"
                    "• Subject Range (Faculty Coverage)\n" 
                    "• Age (Discontinued 2023)\n"
                    "• Research Intensity (Scopus Publications)\n\n"
                    "Prepared by: Dr. Dharmendra Pandey\n"
                    "Deputy Director - QMB & Head - QA\n"
                    "Symbiosis International (Deemed University)")
    
    subtitle.text_frame.paragraphs[0].font.size = Pt(18)
    subtitle.text_frame.paragraphs[0].alignment = PP_ALIGN.LEFT
    
    # Slide 2: Overview of Classification Framework
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = "QS Institution Classifications: Framework Overview"
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = colors['primary']
    
    # Add overview image
    if os.path.exists('QS_Institution_Classifications_Complete_Overview.png'):
        slide.shapes.add_picture('QS_Institution_Classifications_Complete_Overview.png', 
                               Inches(0.5), Inches(1.5), Inches(12), Inches(6.5))
    
    # Slide 3: Size Classification
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = "Size Classification"
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = colors['primary']
    
    # Add size classification table
    if os.path.exists('QS_Size_Classification_Table.png'):
        slide.shapes.add_picture('QS_Size_Classification_Table.png', 
                               Inches(0.5), Inches(1.5), Inches(12), Inches(6))
    
    # Add text box with key insights
    textbox = slide.shapes.add_textbox(Inches(0.5), Inches(7.8), Inches(12), Inches(1))
    text_frame = textbox.text_frame
    text_frame.text = ("Key Insight: Size classification based on Full-Time Equivalent (FTE) student body. "
                      "Where FTE unavailable, estimates based on regional institutional characteristics.")
    text_frame.paragraphs[0].font.size = Pt(14)
    text_frame.paragraphs[0].font.italic = True
    text_frame.paragraphs[0].font.color.rgb = colors['dark']
    
    # Slide 4: Subject Range Classification
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = "Subject Range Classification"
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = colors['secondary']
    
    # Add subject range classification table
    if os.path.exists('QS_Subject_Range_Classification_Table.png'):
        slide.shapes.add_picture('QS_Subject_Range_Classification_Table.png', 
                               Inches(0.5), Inches(1.5), Inches(12), Inches(6))
    
    # Add text box with key insights
    textbox = slide.shapes.add_textbox(Inches(0.5), Inches(7.8), Inches(12), Inches(1))
    text_frame = textbox.text_frame
    text_frame.text = ("Key Insight: Classification based on breadth of academic programs across five faculty areas. "
                      "Medical school presence distinguishes Full Comprehensive (FC) from Comprehensive (CO).")
    text_frame.paragraphs[0].font.size = Pt(14)
    text_frame.paragraphs[0].font.italic = True
    text_frame.paragraphs[0].font.color.rgb = colors['dark']
    
    # Slide 5: Age Classification (Discontinued)
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = "Age Classification (Discontinued Since 2023)"
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = colors['success']
    
    # Add age classification table
    if os.path.exists('QS_Age_Classification_Discontinued.png'):
        slide.shapes.add_picture('QS_Age_Classification_Discontinued.png', 
                               Inches(0.5), Inches(1.5), Inches(12), Inches(6))
    
    # Add text box with important notice
    textbox = slide.shapes.add_textbox(Inches(0.5), Inches(7.8), Inches(12), Inches(1))
    text_frame = textbox.text_frame
    text_frame.text = ("IMPORTANT: Age classification discontinued since 2023 rankings cycle. "
                      "Historical reference only - no longer used in current QS methodology.")
    text_frame.paragraphs[0].font.size = Pt(14)
    text_frame.paragraphs[0].font.bold = True
    text_frame.paragraphs[0].font.color.rgb = colors['success']
    
    # Slide 6: Research Intensity Matrix
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = "Research Intensity Classification Matrix"
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = colors['accent']
    
    # Add research intensity matrix
    if os.path.exists('QS_Research_Intensity_Matrix.png'):
        slide.shapes.add_picture('QS_Research_Intensity_Matrix.png', 
                               Inches(0.5), Inches(1.5), Inches(12), Inches(6))
    
    # Add text box with explanation
    textbox = slide.shapes.add_textbox(Inches(0.5), Inches(7.8), Inches(12), Inches(1))
    text_frame = textbox.text_frame
    text_frame.text = ("Complex matrix combining Subject Focus and Institution Size to determine research thresholds. "
                      "Specialist (SP) institutions use subject-specific mean calculations.")
    text_frame.paragraphs[0].font.size = Pt(14)
    text_frame.paragraphs[0].font.italic = True
    text_frame.paragraphs[0].font.color.rgb = colors['dark']
    
    # Slide 7: Research Intensity Explanation
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = "Research Intensity Levels: Detailed Explanation"
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = colors['accent']
    
    # Add research intensity explanation
    if os.path.exists('QS_Research_Intensity_Explanation.png'):
        slide.shapes.add_picture('QS_Research_Intensity_Explanation.png', 
                               Inches(0.5), Inches(1.5), Inches(12), Inches(6))
    
    # Add text box with methodology note
    textbox = slide.shapes.add_textbox(Inches(0.5), Inches(7.8), Inches(12), Inches(1))
    text_frame = textbox.text_frame
    text_frame.text = ("Methodology: Research intensity evaluated using Scopus database documents over 5-year period. "
                      "Thresholds vary by institution size and subject focus to ensure fair comparison.")
    text_frame.paragraphs[0].font.size = Pt(14)
    text_frame.paragraphs[0].font.italic = True
    text_frame.paragraphs[0].font.color.rgb = colors['dark']
    
    # Save presentation
    prs.save('QS_Institution_Classifications_Complete_Presentation.pptx')
    print("✓ PowerPoint presentation created: QS_Institution_Classifications_Complete_Presentation.pptx")

if __name__ == "__main__":
    print("Creating QS Institution Classifications PowerPoint Presentation...")
    print("=" * 70)
    
    create_qs_classifications_presentation()
    
    print("=" * 70)
    print("PowerPoint presentation created successfully!")
    print("\nPresentation includes:")
    print("• Title slide with framework overview")
    print("• Complete classification framework overview")
    print("• Individual slides for each classification dimension")
    print("• Professional formatting with embedded visualizations")
    print("• Key insights and methodology explanations")
    print("\nFile: QS_Institution_Classifications_Complete_Presentation.pptx")
