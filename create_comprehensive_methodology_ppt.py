"""
Comprehensive QS WUR 2026 Methodology PowerPoint with ALL Lenses, Indicators, and Metrics
Author: Dr. <PERSON><PERSON>, Symbiosis International (Deemed University)
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE

def create_comprehensive_methodology_presentation():
    """Create comprehensive methodology presentation with all QS WUR components."""
    
    # Load existing presentation
    prs = Presentation('QS_WUR_2026_Analysis_SIU_Final.pptx')
    
    # Define SIU brand colors
    siu_blue = RGBColor(30, 64, 175)
    siu_light_blue = RGBColor(59, 130, 246)
    siu_green = RGBColor(16, 185, 129)
    siu_orange = RGBColor(245, 158, 11)
    siu_red = RGBColor(239, 68, 68)
    gray_text = RGBColor(107, 114, 128)
    
    # Remove the old methodology slide (slide 3) and replace with comprehensive version
    # We'll insert new slides after slide 2
    
    # Slide 3: Complete QS WUR 2026 Framework Overview
    slide3_layout = prs.slide_layouts[1]
    slide3 = prs.slides.add_slide(slide3_layout)
    
    title3 = slide3.shapes.title
    title3.text = "Complete QS WUR 2026 Framework: Lenses, Indicators & Metrics"
    title3.text_frame.paragraphs[0].font.size = Pt(28)
    title3.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title3.text_frame.paragraphs[0].font.bold = True
    
    # Create comprehensive framework content
    framework_box = slide3.shapes.add_textbox(Inches(0.3), Inches(1.5), Inches(12.7), Inches(5.8))
    framework_frame = framework_box.text_frame
    framework_frame.word_wrap = True
    
    framework_text = """🔍 RESEARCH AND DISCOVERY LENS (50% Total Weight)
├── Academic Reputation (30%) - Global survey of academics nominating excellent institutions
├── Citations per Faculty (20%) - Research impact normalized by faculty size (Scopus data)

👥 EMPLOYABILITY AND OUTCOMES LENS (20% Total Weight)  
├── Employer Reputation (15%) - Global survey of employers identifying top graduate producers
├── Employment Outcomes (5%) - Alumni Impact Index × ln(Graduate Employment Rate)

🎓 LEARNING EXPERIENCE LENS (10% Total Weight)
├── Faculty Student Ratio (10%) - Academic staff to student ratio indicating teaching resources

🌍 GLOBAL ENGAGEMENT LENS (15% Total Weight)
├── International Faculty Ratio (5%) - Proportion of international academic staff
├── International Student Ratio (5%) - Proportion of international students  
├── International Research Network (5%) - Sustained global research partnerships (3+ joint papers/5 years)

🌱 SUSTAINABILITY LENS (5% Total Weight)
├── Sustainability (5%) - Alumni climate impact + UN SDG-aligned research

📊 NORMALIZATION METHODOLOGY
• Z-score standardization with locked means/standard deviations for top X institutions
• Min-max scaling (1-100) applied to standardized scores
• Weighted combination based on indicator percentages
• Final institutional score undergoes min-max normalization"""
    
    framework_frame.text = framework_text
    
    # Format the framework content
    for i, paragraph in enumerate(framework_frame.paragraphs):
        if 'LENS' in paragraph.text and 'Total Weight' in paragraph.text:
            paragraph.font.size = Pt(16)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_blue
            paragraph.space_after = Pt(8)
        elif paragraph.text.startswith('├──'):
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            paragraph.level = 1
            paragraph.space_after = Pt(4)
        elif 'NORMALIZATION METHODOLOGY' in paragraph.text:
            paragraph.font.size = Pt(16)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_orange
            paragraph.space_before = Pt(12)
            paragraph.space_after = Pt(8)
        elif paragraph.text.startswith('•'):
            paragraph.font.size = Pt(13)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            paragraph.level = 1
            paragraph.space_after = Pt(3)
    
    # Slide 4: Detailed Methodology Changes Table
    slide4_layout = prs.slide_layouts[1]
    slide4 = prs.slides.add_slide(slide4_layout)
    
    title4 = slide4.shapes.title
    title4.text = "QS WUR 2026: Complete Methodology Changes Analysis"
    title4.text_frame.paragraphs[0].font.size = Pt(30)
    title4.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title4.text_frame.paragraphs[0].font.bold = True
    
    # Create comprehensive table for all indicators
    rows, cols = 10, 6
    table_left = Inches(0.2)
    table_top = Inches(1.6)
    table_width = Inches(12.9)
    table_height = Inches(5.2)
    
    table = slide4.shapes.add_table(rows, cols, table_left, table_top, table_width, table_height).table
    
    # Set column widths
    table.columns[0].width = Inches(2.8)  # Lens
    table.columns[1].width = Inches(2.5)  # Indicator
    table.columns[2].width = Inches(1.3)  # 2024 Weight
    table.columns[3].width = Inches(1.3)  # 2025/26 Weight
    table.columns[4].width = Inches(1.0)  # Change
    table.columns[5].width = Inches(4.0)  # Data Source/Description
    
    # Header row
    headers = ['Lens', 'Indicator', '2024\nWeight', '2025/26\nWeight', 'Change', 'Data Source & Description']
    for i, header in enumerate(headers):
        cell = table.cell(0, i)
        cell.text = header
        cell.text_frame.paragraphs[0].font.bold = True
        cell.text_frame.paragraphs[0].font.size = Pt(12)
        cell.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)
        cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        cell.fill.solid()
        cell.fill.fore_color.rgb = siu_blue
    
    # Complete data for all indicators
    data = [
        ['Research &\nDiscovery', 'Academic Reputation', '40%', '30%', '-10%', 'Global academic survey (research excellence)'],
        ['Research &\nDiscovery', 'Citations per Faculty', '20%', '20%', '0%', 'Scopus citation data normalized by faculty'],
        ['Employability &\nOutcomes', 'Employer Reputation', '10%', '15%', '+5%', 'Global employer survey (graduate quality)'],
        ['Employability &\nOutcomes', 'Employment Outcomes', '0%', '5%', '+5%', 'NEW: Alumni Impact × Employment Rate'],
        ['Learning\nExperience', 'Faculty Student Ratio', '20%', '10%', '-10%', 'Academic staff to student ratio'],
        ['Global\nEngagement', 'International Faculty', '5%', '5%', '0%', 'Proportion of international staff'],
        ['Global\nEngagement', 'International Students', '5%', '5%', '0%', 'Proportion of international students'],
        ['Global\nEngagement', 'Intl Research Network', '0%', '5%', '+5%', 'NEW: Sustained global partnerships'],
        ['Sustainability', 'Sustainability', '0%', '5%', '+5%', 'NEW: Climate impact + UN SDG research']
    ]
    
    for i, row_data in enumerate(data, 1):
        for j, cell_data in enumerate(row_data):
            cell = table.cell(i, j)
            cell.text = cell_data
            
            # Font sizing based on column
            if j in [0, 1, 5]:  # Lens, Indicator, Description columns
                cell.text_frame.paragraphs[0].font.size = Pt(10)
            else:  # Weight and change columns
                cell.text_frame.paragraphs[0].font.size = Pt(11)
                cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
            
            # Color coding for changes
            if j == 4:  # Change column
                if cell_data.startswith('+'):
                    cell.text_frame.paragraphs[0].font.color.rgb = siu_green
                    cell.text_frame.paragraphs[0].font.bold = True
                elif cell_data.startswith('-'):
                    cell.text_frame.paragraphs[0].font.color.rgb = siu_red
                    cell.text_frame.paragraphs[0].font.bold = True
                else:
                    cell.text_frame.paragraphs[0].font.color.rgb = gray_text
            
            # Highlight new indicators
            if 'NEW:' in cell_data:
                cell.text_frame.paragraphs[0].font.color.rgb = siu_orange
                cell.text_frame.paragraphs[0].font.bold = True
    
    # Add key insights box
    insights_box = slide4.shapes.add_textbox(Inches(0.5), Inches(6.9), Inches(12), Inches(0.5))
    insights_frame = insights_box.text_frame
    insights_frame.text = "🔑 Key Insight: 50% shift from input metrics (Academic Rep + Faculty Ratio: 60%→40%) to outcome metrics (Employment + Networks + Sustainability: 0%→15%)"
    insights_frame.paragraphs[0].font.size = Pt(14)
    insights_frame.paragraphs[0].font.bold = True
    insights_frame.paragraphs[0].font.color.rgb = siu_orange
    insights_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    
    # Save the updated presentation
    prs.save('QS_WUR_2026_Analysis_SIU_Complete_Methodology.pptx')
    
    return prs

def create_separate_methodology_visualizations():
    """Create separate, focused visualizations for better clarity and space usage."""

    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    import numpy as np

    # Define consistent styling
    plt.style.use('seaborn-v0_8-whitegrid')

    # Colors
    colors = {
        'research': '#1e40af',
        'employability': '#059669',
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }

    # 1. QS WUR 2026 Lens Distribution
    fig1, ax1 = plt.subplots(figsize=(12, 10))

    lenses = ['Research &\nDiscovery\n(50%)', 'Employability &\nOutcomes\n(20%)',
              'Learning\nExperience\n(10%)', 'Global\nEngagement\n(15%)', 'Sustainability\n(5%)']
    weights = [50, 20, 10, 15, 5]
    lens_colors = [colors['research'], colors['employability'], colors['learning'],
                   colors['global'], colors['sustainability']]

    wedges, texts, autotexts = ax1.pie(weights, labels=lenses, autopct='%1.0f%%',
                                      colors=lens_colors, startangle=90,
                                      textprops={'fontsize': 14, 'fontweight': 'bold'},
                                      explode=(0.05, 0.05, 0.05, 0.05, 0.05))

    ax1.set_title('QS World University Rankings 2026\nComplete Lens Distribution',
                 fontsize=20, fontweight='bold', pad=30)

    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(16)

    # Add subtitle
    fig1.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University',
             ha='center', fontsize=12, style='italic')

    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Lens_Distribution.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

    # 2. Complete Indicator Weight Changes
    fig2, ax2 = plt.subplots(figsize=(16, 10))

    indicators = ['Academic\nReputation', 'Citations per\nFaculty', 'Employer\nReputation',
                 'Employment\nOutcomes', 'Faculty Student\nRatio', 'International\nFaculty',
                 'International\nStudents', 'International\nResearch Network', 'Sustainability']

    weights_2024 = [40, 20, 10, 0, 20, 5, 5, 0, 0]
    weights_2026 = [30, 20, 15, 5, 10, 5, 5, 5, 5]
    changes = [w2026 - w2024 for w2024, w2026 in zip(weights_2024, weights_2026)]

    x = np.arange(len(indicators))
    width = 0.35

    bars1 = ax2.bar(x - width/2, weights_2024, width, label='2024 Weights',
                   color='lightsteelblue', alpha=0.8, edgecolor='navy', linewidth=1.5)
    bars2 = ax2.bar(x + width/2, weights_2026, width, label='2025/26 Weights',
                   color='darkblue', alpha=0.9, edgecolor='navy', linewidth=1.5)

    # Add value labels on bars
    for bar in bars1:
        height = bar.get_height()
        if height > 0:
            ax2.annotate(f'{height}%', xy=(bar.get_x() + bar.get_width()/2, height),
                        xytext=(0, 3), textcoords="offset points", ha='center', va='bottom',
                        fontsize=11, fontweight='bold')

    for bar in bars2:
        height = bar.get_height()
        if height > 0:
            ax2.annotate(f'{height}%', xy=(bar.get_x() + bar.get_width()/2, height),
                        xytext=(0, 3), textcoords="offset points", ha='center', va='bottom',
                        fontsize=11, fontweight='bold')

    # Add change indicators
    for i, change in enumerate(changes):
        if change > 0:
            ax2.annotate(f'+{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 3),
                        ha='center', va='bottom', fontweight='bold', color='green',
                        fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7))
        elif change < 0:
            ax2.annotate(f'{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 3),
                        ha='center', va='bottom', fontweight='bold', color='red',
                        fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.7))

    ax2.set_xlabel('QS WUR 2026 Indicators', fontsize=16, fontweight='bold')
    ax2.set_ylabel('Weight Percentage (%)', fontsize=16, fontweight='bold')
    ax2.set_title('QS WUR 2026: Complete Indicator Weight Changes\n2024 vs 2025/26 Methodology',
                 fontsize=18, fontweight='bold', pad=20)
    ax2.set_xticks(x)
    ax2.set_xticklabels(indicators, rotation=45, ha='right', fontsize=12)
    ax2.legend(fontsize=14, loc='upper right')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 50)

    # Add subtitle
    fig2.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University',
             ha='center', fontsize=12, style='italic')

    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Indicator_Changes.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

    # 3. Lens-wise Indicator Breakdown
    fig3, ax3 = plt.subplots(figsize=(14, 10))

    lens_data = {
        'Research & Discovery (50%)': {'indicators': ['Academic Reputation (30%)', 'Citations per Faculty (20%)'],
                                      'weights': [30, 20], 'color': colors['research']},
        'Employability & Outcomes (20%)': {'indicators': ['Employer Reputation (15%)', 'Employment Outcomes (5%)'],
                                          'weights': [15, 5], 'color': colors['employability']},
        'Learning Experience (10%)': {'indicators': ['Faculty Student Ratio (10%)'],
                                     'weights': [10], 'color': colors['learning']},
        'Global Engagement (15%)': {'indicators': ['International Faculty (5%)', 'International Students (5%)', 'International Research Network (5%)'],
                                   'weights': [5, 5, 5], 'color': colors['global']},
        'Sustainability (5%)': {'indicators': ['Sustainability (5%)'],
                               'weights': [5], 'color': colors['sustainability']}
    }

    y_positions = np.arange(len(lens_data))
    bar_height = 0.6

    for i, (lens, data) in enumerate(lens_data.items()):
        # Main lens bar
        ax3.barh(y_positions[i], sum(data['weights']), height=bar_height,
                color=data['color'], alpha=0.8, edgecolor='black', linewidth=2)

        # Lens label
        ax3.text(sum(data['weights'])/2, y_positions[i], lens,
                ha='center', va='center', fontweight='bold', fontsize=12, color='white')

        # Individual indicator bars
        x_offset = 0
        for j, (indicator, weight) in enumerate(zip(data['indicators'], data['weights'])):
            ax3.barh(y_positions[i] - 0.25, weight, left=x_offset, height=0.15,
                    color=data['color'], alpha=0.5, edgecolor='black', linewidth=1)

            # Indicator labels
            ax3.text(x_offset + weight/2, y_positions[i] - 0.25, indicator,
                    ha='center', va='center', fontsize=9, fontweight='bold')
            x_offset += weight

    ax3.set_xlim(0, 55)
    ax3.set_ylim(-0.5, len(lens_data) - 0.5)
    ax3.set_xlabel('Weight Percentage (%)', fontsize=16, fontweight='bold')
    ax3.set_title('QS WUR 2026: Lens-wise Indicator Breakdown\nHierarchical Structure of Methodology',
                 fontsize=18, fontweight='bold', pad=20)
    ax3.set_yticks([])
    ax3.grid(True, alpha=0.3, axis='x')

    # Add subtitle
    fig3.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University',
             ha='center', fontsize=12, style='italic')

    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Lens_Breakdown.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

    # 4. Methodology Process Flow
    fig4, ax4 = plt.subplots(figsize=(12, 14))

    process_steps = [
        {"title": "1. Data Collection",
         "details": ["Academic/Employer Surveys", "Scopus Citation Database", "Institutional Data Submissions", "Alumni Tracking Systems"]},
        {"title": "2. Raw Data Processing",
         "details": ["Ratio Calculations", "Capping & Damping", "Quality Validation", "Missing Data Handling"]},
        {"title": "3. Z-Score Normalization",
         "details": ["Standardization Process", "Locked Means/Standard Deviations", "Normal Distribution Mapping", "Top X Institution Benchmarking"]},
        {"title": "4. Min-Max Scaling",
         "details": ["0-1 Range Conversion", "1-100 Final Scaling", "Weighted Combination", "Indicator Score Calculation"]},
        {"title": "5. Final Ranking",
         "details": ["Overall Score Calculation", "Final Min-Max Normalization", "Rank Assignment", "Quality Assurance"]}
    ]

    step_colors = [colors['research'], colors['employability'], colors['learning'],
                   colors['global'], colors['sustainability']]

    for i, step in enumerate(process_steps):
        y_position = 0.9 - (i * 0.18)

        # Main process box
        main_box = patches.FancyBboxPatch((0.1, y_position - 0.04), 0.8, 0.08,
                                         boxstyle="round,pad=0.01",
                                         facecolor=step_colors[i],
                                         alpha=0.8, edgecolor='black', linewidth=2)
        ax4.add_patch(main_box)

        # Step title
        ax4.text(0.5, y_position, step["title"], ha='center', va='center',
                fontsize=16, fontweight='bold', color='white', transform=ax4.transAxes)

        # Detail boxes
        for j, detail in enumerate(step["details"]):
            detail_y = y_position - 0.08 - (j * 0.025)
            detail_box = patches.FancyBboxPatch((0.15, detail_y - 0.01), 0.7, 0.02,
                                               boxstyle="round,pad=0.005",
                                               facecolor=step_colors[i],
                                               alpha=0.3, edgecolor='gray')
            ax4.add_patch(detail_box)

            ax4.text(0.5, detail_y, f"• {detail}", ha='center', va='center',
                    fontsize=11, fontweight='bold', transform=ax4.transAxes)

        # Arrow to next step
        if i < len(process_steps) - 1:
            ax4.annotate('', xy=(0.5, y_position - 0.12), xytext=(0.5, y_position - 0.08),
                        arrowprops=dict(arrowstyle='->', lw=3, color='black'),
                        transform=ax4.transAxes)

    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.set_title('QS WUR 2026: Complete Methodology Process Flow\nFrom Data Collection to Final Rankings',
                 fontsize=18, fontweight='bold', pad=20)
    ax4.axis('off')

    # Add subtitle
    fig4.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University',
             ha='center', fontsize=12, style='italic')

    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Process_Flow.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Creating comprehensive QS WUR 2026 methodology presentation with separate visualizations...")

    # Create the comprehensive methodology presentation
    prs = create_comprehensive_methodology_presentation()

    # Create separate, focused methodology visualizations
    create_separate_methodology_visualizations()

    print("✓ Comprehensive methodology presentation created: QS_WUR_2026_Analysis_SIU_Complete_Methodology.pptx")
    print("✓ Separate methodology visualizations created:")
    print("  • QS_WUR_2026_Lens_Distribution.png - Clear lens weight distribution")
    print("  • QS_WUR_2026_Indicator_Changes.png - All indicator weight changes")
    print("  • QS_WUR_2026_Lens_Breakdown.png - Hierarchical lens-indicator structure")
    print("  • QS_WUR_2026_Process_Flow.png - Complete methodology process")
    print("\nFeatures:")
    print("• All 5 lenses with complete descriptions")
    print("• All 9 indicators with data sources")
    print("• Complete normalization methodology")
    print("• Detailed weight change analysis")
    print("• Separate, focused visualizations for clarity")
    print("• Efficient use of space in each chart")
    print("\n🎯 Ready for Directors Meeting with clear, focused methodology coverage!")
