"""
Fix QS WUR 2026 Process Flow Chart - Better Spacing and Clarity
Author: Dr<PERSON>, Symbiosis International (Deemed University)
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

def create_improved_process_flow():
    """Create an optimized process flow chart with better space utilization."""

    # Set up styling
    plt.style.use('seaborn-v0_8-whitegrid')

    # Colors
    colors = {
        'research': '#1e40af',
        'employability': '#059669',
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }

    # Create figure with optimized proportions
    fig, ax = plt.subplots(figsize=(14, 16))

    process_steps = [
        {
            "title": "1. DATA COLLECTION",
            "details": [
                "Academic Reputation Survey (60,000+ academics)",
                "Employer Reputation Survey (40,000+ employers)",
                "Scopus Citation Database Analysis",
                "Institutional Data Submissions",
                "Alumni Employment Tracking"
            ]
        },
        {
            "title": "2. RAW DATA PROCESSING",
            "details": [
                "Calculate Faculty-Student Ratios",
                "Compute International Staff/Student Percentages",
                "Apply Capping & Damping Mechanisms",
                "Data Quality Validation & Cleaning",
                "Missing Data Imputation"
            ]
        },
        {
            "title": "3. Z-SCORE NORMALIZATION",
            "details": [
                "Standardization: (X - μ) / σ",
                "Locked Means/Standard Deviations",
                "Top X Institution Benchmarking",
                "Normal Distribution Mapping",
                "Outlier Adjustment"
            ]
        },
        {
            "title": "4. MIN-MAX SCALING",
            "details": [
                "Convert Z-scores to 0-1 Range",
                "Apply 1-100 Final Scaling",
                "Weight Individual Indicators",
                "Calculate Lens Scores",
                "Combine Weighted Indicators"
            ]
        },
        {
            "title": "5. FINAL RANKING",
            "details": [
                "Sum All Weighted Lens Scores",
                "Apply Final Min-Max Normalization",
                "Assign Global Rank Positions",
                "Quality Assurance Checks",
                "Publish Final Rankings"
            ]
        }
    ]

    step_colors = [colors['research'], colors['employability'], colors['learning'],
                   colors['global'], colors['sustainability']]

    # Optimized spacing - use more of the available space
    chart_start_y = 0.95  # Start higher
    chart_end_y = 0.08    # End lower
    total_chart_height = chart_start_y - chart_end_y
    step_spacing = total_chart_height / len(process_steps)

    for i, step in enumerate(process_steps):
        # Calculate positions with better space utilization
        y_top = chart_start_y - (i * step_spacing)
        y_bottom = y_top - (step_spacing * 0.85)  # Use 85% of available space

        # Larger main step box
        main_box_height = 0.06
        main_box_y = y_top - main_box_height

        main_box = patches.FancyBboxPatch(
            (0.08, main_box_y), 0.84, main_box_height,
            boxstyle="round,pad=0.01",
            facecolor=step_colors[i],
            alpha=0.9,
            edgecolor='black',
            linewidth=2
        )
        ax.add_patch(main_box)

        # Step title with larger font
        ax.text(0.5, main_box_y + main_box_height/2, step["title"],
                ha='center', va='center',
                fontsize=18, fontweight='bold', color='white',
                transform=ax.transAxes)

        # Detail boxes with optimized spacing
        detail_box_height = 0.032
        detail_spacing = 0.006
        details_start_y = main_box_y - 0.015

        for j, detail in enumerate(step["details"]):
            detail_y = details_start_y - (j * (detail_box_height + detail_spacing))

            # Wider detail background box
            detail_box = patches.FancyBboxPatch(
                (0.12, detail_y), 0.76, detail_box_height,
                boxstyle="round,pad=0.005",
                facecolor=step_colors[i],
                alpha=0.25,
                edgecolor=step_colors[i],
                linewidth=1.5
            )
            ax.add_patch(detail_box)

            # Detail text with better formatting
            ax.text(0.5, detail_y + detail_box_height/2, f"• {detail}",
                    ha='center', va='center',
                    fontsize=12, fontweight='normal',
                    color='black',
                    transform=ax.transAxes)

        # Improved arrow to next step
        if i < len(process_steps) - 1:
            arrow_start_y = y_bottom + 0.015
            arrow_end_y = arrow_start_y - 0.025

            ax.annotate('',
                       xy=(0.5, arrow_end_y),
                       xytext=(0.5, arrow_start_y),
                       arrowprops=dict(
                           arrowstyle='->',
                           lw=5,
                           color='darkblue',
                           alpha=0.9
                       ),
                       transform=ax.transAxes)

    # Optimized title positioning - much closer to chart
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.text(0.5, 0.99, 'QS World University Rankings 2026',
            ha='center', va='top', fontsize=22, fontweight='bold',
            transform=ax.transAxes, color='#1e40af')
    ax.text(0.5, 0.97, 'Complete Methodology Process Flow',
            ha='center', va='top', fontsize=18, fontweight='bold',
            transform=ax.transAxes, color='#374151')
    ax.axis('off')

    # Bottom subtitle
    ax.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University',
            ha='center', va='bottom', fontsize=12, style='italic',
            transform=ax.transAxes, color='#6b7280')

    # Enhanced side annotations
    ax.text(0.02, 0.5, 'DATA\nINPUTS', ha='center', va='center',
            fontsize=16, fontweight='bold', rotation=90,
            transform=ax.transAxes, color='#1e40af',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.3))

    ax.text(0.98, 0.5, 'RANKING\nOUTPUTS', ha='center', va='center',
            fontsize=16, fontweight='bold', rotation=90,
            transform=ax.transAxes, color='#1e40af',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.3))

    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Process_Flow_Optimized.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_horizontal_process_flow():
    """Create an optimized horizontal process flow with better space utilization."""

    # Set up styling
    plt.style.use('seaborn-v0_8-whitegrid')

    # Colors
    colors = {
        'research': '#1e40af',
        'employability': '#059669',
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }

    # Create optimized horizontal figure
    fig, ax = plt.subplots(figsize=(20, 10))

    process_steps = [
        {
            "title": "DATA\nCOLLECTION",
            "details": [
                "Academic Survey",
                "Employer Survey",
                "Scopus Citations",
                "Institutional Data"
            ]
        },
        {
            "title": "DATA\nPROCESSING",
            "details": [
                "Ratio Calculations",
                "Quality Validation",
                "Capping & Damping",
                "Missing Data Handling"
            ]
        },
        {
            "title": "Z-SCORE\nNORMALIZATION",
            "details": [
                "Standardization",
                "Locked Benchmarks",
                "Distribution Mapping",
                "Outlier Adjustment"
            ]
        },
        {
            "title": "MIN-MAX\nSCALING",
            "details": [
                "0-1 Conversion",
                "1-100 Scaling",
                "Weight Application",
                "Score Combination"
            ]
        },
        {
            "title": "FINAL\nRANKING",
            "details": [
                "Score Summation",
                "Final Normalization",
                "Rank Assignment",
                "Quality Assurance"
            ]
        }
    ]

    step_colors = [colors['research'], colors['employability'], colors['learning'],
                   colors['global'], colors['sustainability']]

    # Optimized horizontal spacing - use more width
    step_width = 0.17
    step_spacing = 0.015
    start_x = 0.03

    for i, step in enumerate(process_steps):
        # Calculate x position with better spacing
        x_left = start_x + (i * (step_width + step_spacing))

        # Larger main step box
        main_box = patches.FancyBboxPatch(
            (x_left, 0.65), step_width, 0.25,
            boxstyle="round,pad=0.01",
            facecolor=step_colors[i],
            alpha=0.9,
            edgecolor='black',
            linewidth=2
        )
        ax.add_patch(main_box)

        # Step title with larger font
        ax.text(x_left + step_width/2, 0.775, step["title"],
                ha='center', va='center',
                fontsize=16, fontweight='bold', color='white',
                transform=ax.transAxes)

        # Optimized detail boxes
        detail_box_height = 0.08
        detail_spacing_y = 0.02
        details_start_y = 0.55

        for j, detail in enumerate(step["details"]):
            detail_y = details_start_y - (j * (detail_box_height + detail_spacing_y))

            # Larger detail box
            detail_box = patches.FancyBboxPatch(
                (x_left + 0.005, detail_y), step_width - 0.01, detail_box_height,
                boxstyle="round,pad=0.005",
                facecolor=step_colors[i],
                alpha=0.3,
                edgecolor=step_colors[i],
                linewidth=1.5
            )
            ax.add_patch(detail_box)

            # Detail text with better formatting
            ax.text(x_left + step_width/2, detail_y + detail_box_height/2, detail,
                    ha='center', va='center',
                    fontsize=11, fontweight='bold',
                    color='black',
                    transform=ax.transAxes)

        # Enhanced arrow to next step
        if i < len(process_steps) - 1:
            arrow_x = x_left + step_width + step_spacing/2
            ax.annotate('',
                       xy=(arrow_x + 0.008, 0.775),
                       xytext=(arrow_x - 0.008, 0.775),
                       arrowprops=dict(
                           arrowstyle='->',
                           lw=5,
                           color='darkblue',
                           alpha=0.9
                       ),
                       transform=ax.transAxes)

    # Optimized title positioning
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.text(0.5, 0.98, 'QS World University Rankings 2026: Methodology Process Flow',
            ha='center', va='top', fontsize=20, fontweight='bold',
            transform=ax.transAxes, color='#1e40af')
    ax.text(0.5, 0.95, 'From Raw Data to Final Rankings',
            ha='center', va='top', fontsize=16, fontweight='bold',
            transform=ax.transAxes, color='#374151')
    ax.axis('off')

    # Bottom subtitle
    ax.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University',
            ha='center', va='bottom', fontsize=12, style='italic',
            transform=ax.transAxes, color='#6b7280')

    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Process_Flow_Horizontal_Optimized.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Creating space-optimized QS WUR 2026 process flow charts...")

    # Create optimized vertical process flow
    create_improved_process_flow()
    print("✓ Space-optimized vertical process flow: QS_WUR_2026_Process_Flow_Optimized.png")

    # Create optimized horizontal process flow
    create_horizontal_process_flow()
    print("✓ Space-optimized horizontal process flow: QS_WUR_2026_Process_Flow_Horizontal_Optimized.png")

    print("\nOptimizations applied:")
    print("• Minimal title-to-chart spacing")
    print("• Maximum use of available canvas space")
    print("• Larger, more readable text and boxes")
    print("• Enhanced visual appeal and clarity")
    print("• Professional presentation quality")
    print("\n🎯 Process flow charts now optimally use space for maximum visual impact!")
