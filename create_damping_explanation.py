"""
Create QS WUR Damping Explanation Slide and Visualization
Author: Dr<PERSON>, Symbiosis International (Deemed University)

This script creates an easy-to-understand explanation of damping in QS WUR methodology
and its impact on university rankings.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import seaborn as sns
from matplotlib.patches import FancyBboxPatch

def create_clean_damping_explanation():
    """Create clean, well-organized damping explanation visualization."""

    plt.style.use('seaborn-v0_8-whitegrid')

    # Colors
    colors = {
        'primary': '#1e40af',
        'secondary': '#3b82f6',
        'accent': '#10b981',
        'warning': '#f59e0b',
        'danger': '#ef4444',
        'light': '#f8fafc',
        'dark': '#1f2937'
    }

    # Create figure with 2x2 subplots with better spacing
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
    fig.suptitle('QS WUR Damping Mechanism: Easy Explanation for Directors',
                 fontsize=20, fontweight='bold', y=0.96, color=colors['primary'])

    # 1. What is Damping? - Clean conceptual explanation
    ax1.text(0.5, 0.92, 'What is Damping?', ha='center', va='center',
            fontsize=16, fontweight='bold', transform=ax1.transAxes, color=colors['primary'])

    # Simple definition box
    definition_box = FancyBboxPatch((0.05, 0.7), 0.9, 0.15,
                                   boxstyle="round,pad=0.02",
                                   facecolor=colors['light'],
                                   alpha=0.8, edgecolor=colors['primary'], linewidth=2)
    ax1.add_patch(definition_box)

    ax1.text(0.5, 0.775, 'Statistical technique that reduces extreme values\nto ensure fair university comparisons',
            ha='center', va='center', fontsize=13, fontweight='bold',
            transform=ax1.transAxes, color=colors['dark'])

    # Process flow - simplified
    process_steps = ["Raw Data", "Apply Damping", "Fair Scores"]
    step_colors = [colors['secondary'], colors['warning'], colors['accent']]

    for i, (step, color) in enumerate(zip(process_steps, step_colors)):
        y_pos = 0.5 - (i * 0.15)

        # Step box
        step_box = FancyBboxPatch((0.15, y_pos - 0.04), 0.7, 0.08,
                                 boxstyle="round,pad=0.01",
                                 facecolor=color,
                                 alpha=0.8, edgecolor='black', linewidth=1)
        ax1.add_patch(step_box)

        ax1.text(0.5, y_pos, step, ha='center', va='center',
                fontsize=12, fontweight='bold', color='white', transform=ax1.transAxes)

        # Arrow to next step
        if i < len(process_steps) - 1:
            ax1.annotate('', xy=(0.5, y_pos - 0.08), xytext=(0.5, y_pos - 0.05),
                        arrowprops=dict(arrowstyle='->', lw=2, color='black'),
                        transform=ax1.transAxes)

    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    
    # 2. Simple Before/After Example
    ax2.text(0.5, 0.92, 'Simple Example: Before vs After Damping', ha='center', va='center',
            fontsize=16, fontweight='bold', transform=ax2.transAxes, color=colors['primary'])

    # Clean example with just 3 universities
    universities = ['Top Uni', 'SIU', 'Extreme Uni']
    raw_citations = [85, 35, 180]
    damped_citations = [75, 34, 65]

    x = np.arange(len(universities))
    width = 0.35

    bars1 = ax2.bar(x - width/2, raw_citations, width, label='Before Damping',
                   color=colors['danger'], alpha=0.8, edgecolor='black')
    bars2 = ax2.bar(x + width/2, damped_citations, width, label='After Damping',
                   color=colors['accent'], alpha=0.8, edgecolor='black')

    # Clean value labels
    for i, (raw, damped) in enumerate(zip(raw_citations, damped_citations)):
        ax2.text(i - width/2, raw + 3, str(raw), ha='center', va='bottom',
                fontweight='bold', fontsize=11)
        ax2.text(i + width/2, damped + 3, str(damped), ha='center', va='bottom',
                fontweight='bold', fontsize=11)

    ax2.set_xlabel('Universities', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Citations per Faculty', fontsize=12, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(universities, fontsize=11)
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 200)

    # Add insight box
    insight_box = FancyBboxPatch((0.02, 0.02), 0.96, 0.15,
                                boxstyle="round,pad=0.02",
                                facecolor='lightyellow',
                                alpha=0.8, edgecolor=colors['warning'], linewidth=2)
    ax2.add_patch(insight_box)

    ax2.text(0.5, 0.095, 'Key Insight: Extreme values reduced, fair gaps maintained\nSIU benefits from more level playing field',
            ha='center', va='center', fontsize=11, fontweight='bold',
            transform=ax2.transAxes, color=colors['dark'])
    
    # 3. Benefits for SIU - Clean presentation
    ax3.text(0.5, 0.92, 'Why Damping Benefits SIU', ha='center', va='center',
            fontsize=16, fontweight='bold', transform=ax3.transAxes, color=colors['primary'])

    # Benefits list with proper spacing
    benefits = [
        "Levels playing field vs extreme performers",
        "Rewards balanced institutional excellence",
        "Reduces ranking volatility from outliers",
        "Supports multidisciplinary approach"
    ]

    benefit_colors = [colors['accent'], colors['secondary'], colors['warning'], colors['primary']]

    for i, (benefit, color) in enumerate(zip(benefits, benefit_colors)):
        y_pos = 0.75 - (i * 0.15)

        # Benefit box with proper sizing
        benefit_box = FancyBboxPatch((0.05, y_pos - 0.05), 0.9, 0.08,
                                    boxstyle="round,pad=0.02",
                                    facecolor=color,
                                    alpha=0.2, edgecolor=color, linewidth=2)
        ax3.add_patch(benefit_box)

        # Checkmark (using simple text instead of unicode)
        ax3.text(0.1, y_pos, '√', ha='center', va='center',
                fontsize=16, fontweight='bold', color=color, transform=ax3.transAxes)

        # Benefit text with proper wrapping
        ax3.text(0.15, y_pos, benefit, ha='left', va='center',
                fontsize=11, fontweight='bold', color=colors['dark'], transform=ax3.transAxes)

    # Bottom insight
    bottom_box = FancyBboxPatch((0.05, 0.05), 0.9, 0.12,
                               boxstyle="round,pad=0.02",
                               facecolor='lightblue',
                               alpha=0.3, edgecolor=colors['primary'], linewidth=2)
    ax3.add_patch(bottom_box)

    ax3.text(0.5, 0.11, 'Bottom Line: Damping supports SIU\'s comprehensive\nexcellence strategy over narrow specialization',
            ha='center', va='center', fontsize=12, fontweight='bold',
            transform=ax3.transAxes, color=colors['primary'])
    
    # 4. Strategic Actions for SIU - Clean layout
    ax4.text(0.5, 0.92, 'Strategic Actions for SIU', ha='center', va='center',
            fontsize=16, fontweight='bold', transform=ax4.transAxes, color=colors['primary'])

    # Action items with clean presentation
    actions = [
        "Build consistent performance across ALL indicators",
        "Avoid over-investment in single metrics",
        "Focus on balanced institutional excellence",
        "Leverage multidisciplinary strengths"
    ]

    action_colors = [colors['primary'], colors['accent'], colors['secondary'], colors['warning']]

    for i, (action, color) in enumerate(zip(actions, action_colors)):
        y_pos = 0.75 - (i * 0.15)

        # Action number circle
        circle = plt.Circle((0.08, y_pos), 0.03, facecolor=color, alpha=0.8,
                           edgecolor='black', linewidth=1, transform=ax4.transAxes)
        ax4.add_patch(circle)

        ax4.text(0.08, y_pos, str(i+1), ha='center', va='center',
                fontsize=12, fontweight='bold', color='white', transform=ax4.transAxes)

        # Action text box
        action_box = FancyBboxPatch((0.15, y_pos - 0.04), 0.8, 0.08,
                                   boxstyle="round,pad=0.02",
                                   facecolor=color,
                                   alpha=0.15, edgecolor=color, linewidth=1)
        ax4.add_patch(action_box)

        # Action text
        ax4.text(0.17, y_pos, action, ha='left', va='center',
                fontsize=11, fontweight='bold', color=colors['dark'], transform=ax4.transAxes)

    # Key takeaway box
    takeaway_box = FancyBboxPatch((0.05, 0.05), 0.9, 0.15,
                                 boxstyle="round,pad=0.02",
                                 facecolor='lightgreen',
                                 alpha=0.3, edgecolor=colors['accent'], linewidth=2)
    ax4.add_patch(takeaway_box)

    ax4.text(0.5, 0.125, 'KEY TAKEAWAY\nDamping favors SIU\'s balanced approach over\nnarrow specialization strategies',
            ha='center', va='center', fontsize=12, fontweight='bold',
            transform=ax4.transAxes, color=colors['dark'])

    # Set axis properties for all subplots
    for ax in [ax1, ax2, ax3, ax4]:
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')

    plt.subplots_adjust(top=0.92, bottom=0.05, left=0.05, right=0.95, hspace=0.3, wspace=0.2)
    plt.savefig('QS_WUR_2026_Damping_Explanation_Clean.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_clean_damping_process():
    """Create clean, well-organized damping process flow."""

    plt.style.use('seaborn-v0_8-whitegrid')

    colors = {
        'primary': '#1e40af',
        'secondary': '#3b82f6',
        'accent': '#10b981',
        'warning': '#f59e0b',
        'danger': '#ef4444'
    }

    fig, ax = plt.subplots(figsize=(16, 12))

    # Clean title
    ax.text(0.5, 0.96, 'QS WUR Damping Process: Simple Step-by-Step Guide',
            ha='center', va='top', fontsize=20, fontweight='bold',
            transform=ax.transAxes, color=colors['primary'])

    # Simplified process steps
    steps = [
        {
            "title": "1. COLLECT RAW DATA",
            "description": "Gather citations, ratios, survey data",
            "example": "SIU: 35 citations/faculty",
            "y": 0.8
        },
        {
            "title": "2. IDENTIFY EXTREMES",
            "description": "Find unusually high/low values",
            "example": "Extreme Uni: 180 citations/faculty",
            "y": 0.65
        },
        {
            "title": "3. APPLY DAMPING",
            "description": "Reduce extreme values using formula",
            "example": "180 → 65 (reduced by damping)",
            "y": 0.5
        },
        {
            "title": "4. NORMALIZE SCORES",
            "description": "Convert to 1-100 scale with weights",
            "example": "SIU: 34 → Score 45/100",
            "y": 0.35
        },
        {
            "title": "5. FINAL RANKING",
            "description": "Combine all indicators for rank",
            "example": "SIU: Final Rank 696",
            "y": 0.2
        }
    ]

    step_colors = [colors['secondary'], colors['warning'], colors['danger'], colors['accent'], colors['primary']]

    for i, step in enumerate(steps):
        # Main step box - properly sized
        main_box = FancyBboxPatch((0.05, step["y"] - 0.05), 0.35, 0.08,
                                 boxstyle="round,pad=0.02",
                                 facecolor=step_colors[i],
                                 alpha=0.9, edgecolor='black', linewidth=2)
        ax.add_patch(main_box)

        # Step title - fits in box
        ax.text(0.225, step["y"] - 0.01, step["title"], ha='center', va='center',
                fontsize=13, fontweight='bold', color='white', transform=ax.transAxes)

        # Description box - properly sized
        desc_box = FancyBboxPatch((0.42, step["y"] - 0.05), 0.35, 0.08,
                                 boxstyle="round,pad=0.02",
                                 facecolor=step_colors[i],
                                 alpha=0.2, edgecolor=step_colors[i], linewidth=1)
        ax.add_patch(desc_box)

        # Description text - fits in box
        ax.text(0.595, step["y"] - 0.01, step["description"], ha='center', va='center',
                fontsize=11, fontweight='normal', color=colors['dark'], transform=ax.transAxes)

        # Example box - properly sized
        example_box = FancyBboxPatch((0.79, step["y"] - 0.05), 0.19, 0.08,
                                    boxstyle="round,pad=0.02",
                                    facecolor='lightgray',
                                    alpha=0.8, edgecolor='black', linewidth=1)
        ax.add_patch(example_box)

        # Example text - fits in box
        ax.text(0.885, step["y"] - 0.01, step["example"], ha='center', va='center',
                fontsize=10, fontweight='bold', color='black', transform=ax.transAxes)

        # Clean arrow to next step
        if i < len(steps) - 1:
            ax.annotate('', xy=(0.225, step["y"] - 0.08), xytext=(0.225, step["y"] - 0.06),
                       arrowprops=dict(arrowstyle='->', lw=3, color='black'),
                       transform=ax.transAxes)

    # Clean column headers
    header_y = 0.88
    ax.text(0.225, header_y, 'PROCESS STEP', ha='center', va='center',
            fontsize=14, fontweight='bold', transform=ax.transAxes, color=colors['primary'])
    ax.text(0.595, header_y, 'WHAT HAPPENS', ha='center', va='center',
            fontsize=14, fontweight='bold', transform=ax.transAxes, color=colors['primary'])
    ax.text(0.885, header_y, 'EXAMPLE', ha='center', va='center',
            fontsize=14, fontweight='bold', transform=ax.transAxes, color=colors['primary'])

    # Clean key takeaway box
    takeaway_box = FancyBboxPatch((0.1, 0.05), 0.8, 0.08,
                                 boxstyle="round,pad=0.02",
                                 facecolor='lightblue',
                                 alpha=0.8, edgecolor=colors['primary'], linewidth=2)
    ax.add_patch(takeaway_box)

    ax.text(0.5, 0.09, 'KEY RESULT: Damping creates fairer rankings by preventing extreme outliers\nfrom dominating. This benefits balanced institutions like SIU.',
            ha='center', va='center', fontsize=13, fontweight='bold',
            transform=ax.transAxes, color=colors['primary'])

    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')

    plt.subplots_adjust(top=0.92, bottom=0.08, left=0.05, right=0.95)
    plt.savefig('QS_WUR_2026_Damping_Process_Clean.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Creating clean QS WUR damping explanation visualizations...")

    # Create clean damping explanation
    create_clean_damping_explanation()
    print("✓ Clean damping explanation created: QS_WUR_2026_Damping_Explanation_Clean.png")

    # Create clean process flow
    create_clean_damping_process()
    print("✓ Clean damping process flow created: QS_WUR_2026_Damping_Process_Clean.png")

    print("\nClean damping explanation visualizations created:")
    print("• Well-organized 4-panel explanation with proper text fitting")
    print("• Clean step-by-step process flow with no text overflow")
    print("• Strategic implications clearly presented")
    print("• Professional format suitable for directors")
    print("• All text properly contained within boxes")
    print("\n🎯 Clean damping explanation ready for Directors Meeting!")
