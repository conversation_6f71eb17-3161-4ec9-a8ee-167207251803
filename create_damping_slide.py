"""
Create PowerPoint Slide for QS WUR Damping Explanation
Author: Dr<PERSON>, Symbiosis International (Deemed University)
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor

def add_damping_slide_to_presentation():
    """Add damping explanation slide to existing presentation."""
    
    # Load existing presentation
    try:
        prs = Presentation('QS_WUR_2026_Analysis_SIU_Complete_Methodology.pptx')
    except:
        # Create new presentation if file doesn't exist
        prs = Presentation()
    
    # Define colors
    siu_blue = RGBColor(30, 64, 175)
    siu_light_blue = RGBColor(59, 130, 246)
    siu_green = RGBColor(16, 185, 129)
    siu_orange = RGBColor(245, 158, 11)
    siu_red = RGBColor(239, 68, 68)
    gray_text = RGBColor(107, 114, 128)
    
    # Add damping explanation slide
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = "Understanding Damping in QS WUR: Impact on Rankings"
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title.text_frame.paragraphs[0].font.bold = True
    
    # Create content areas
    content_box = slide.shapes.add_textbox(Inches(0.5), Inches(1.8), Inches(12), Inches(5.5))
    content_frame = content_box.text_frame
    content_frame.word_wrap = True
    
    content_text = """What is Damping?
• Statistical technique used by QS to reduce the impact of extreme outlier values
• Prevents universities with unusually high performance in single indicators from dominating rankings
• Ensures fair comparison across different university sizes and types
• Applied before normalization and scoring processes

How Damping Works - Simple Example:
• University A: 180 citations per faculty (extreme outlier)
• University B: 35 citations per faculty (normal range)
• After damping: University A → 65, University B → 34
• Relative performance maintained but extreme gap reduced

Why Damping Matters for SIU:
• Levels the playing field against institutions with extreme single-metric performance
• Rewards balanced excellence across all QS indicators
• Reduces volatility in rankings due to outlier institutions
• Encourages comprehensive institutional development rather than narrow focus

Strategic Implications:
• Focus on consistent performance across ALL indicators
• Avoid over-investment in single metrics at expense of others
• Build balanced institutional excellence
• Understand that extreme performance in one area has diminishing returns

Key Takeaway for SIU Strategy:
Damping mechanism supports SIU's multidisciplinary approach and rewards comprehensive excellence across all QS WUR indicators rather than narrow specialization."""
    
    content_frame.text = content_text
    
    # Format content with different styles
    for i, paragraph in enumerate(content_frame.paragraphs):
        if paragraph.text.endswith(':') or paragraph.text.endswith('?'):
            paragraph.font.size = Pt(18)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_blue
            paragraph.space_after = Pt(8)
        elif paragraph.text.startswith('•'):
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            paragraph.level = 1
            paragraph.space_after = Pt(4)
        elif 'Key Takeaway' in paragraph.text:
            paragraph.font.size = Pt(16)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_orange
            paragraph.space_before = Pt(12)
        else:
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # Save the updated presentation
    prs.save('QS_WUR_2026_Analysis_SIU_With_Damping.pptx')
    
    return prs

def create_standalone_damping_presentation():
    """Create a standalone presentation focused on damping."""
    
    prs = Presentation()
    
    # Define colors
    siu_blue = RGBColor(30, 64, 175)
    siu_light_blue = RGBColor(59, 130, 246)
    siu_green = RGBColor(16, 185, 129)
    siu_orange = RGBColor(245, 158, 11)
    
    # Slide 1: Title Slide
    slide1_layout = prs.slide_layouts[0]
    slide1 = prs.slides.add_slide(slide1_layout)
    
    title1 = slide1.shapes.title
    subtitle1 = slide1.placeholders[1]
    
    title1.text = "Understanding Damping in QS WUR"
    title1.text_frame.paragraphs[0].font.size = Pt(44)
    title1.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title1.text_frame.paragraphs[0].font.bold = True
    
    subtitle1.text = "Impact on University Rankings and Strategic Implications\n\nSymbiosis International University\nDirectors Meeting"
    for paragraph in subtitle1.text_frame.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = RGBColor(107, 114, 128)
        paragraph.alignment = PP_ALIGN.CENTER
    
    # Slide 2: Damping Explanation
    slide2_layout = prs.slide_layouts[1]
    slide2 = prs.slides.add_slide(slide2_layout)
    
    title2 = slide2.shapes.title
    title2.text = "What is Damping and Why Does it Matter?"
    title2.text_frame.paragraphs[0].font.size = Pt(32)
    title2.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title2.text_frame.paragraphs[0].font.bold = True
    
    # Create two-column layout
    left_box = slide2.shapes.add_textbox(Inches(0.5), Inches(1.8), Inches(5.5), Inches(5))
    left_frame = left_box.text_frame
    left_frame.word_wrap = True
    
    left_text = """Definition & Purpose
• Statistical technique to reduce extreme outlier impact
• Prevents single-metric dominance in rankings
• Ensures fair comparison across university types
• Applied before normalization process

How It Works
• Identifies extreme high/low values
• Applies logarithmic or capping formulas
• Reduces gaps between outliers and normal range
• Maintains relative performance order

Example Impact
• Before: Uni A (180), SIU (35) citations/faculty
• After: Uni A (65), SIU (34) citations/faculty
• Gap reduced from 145 to 31 points"""
    
    left_frame.text = left_text
    
    right_box = slide2.shapes.add_textbox(Inches(6.5), Inches(1.8), Inches(5.5), Inches(5))
    right_frame = right_box.text_frame
    right_frame.word_wrap = True
    
    right_text = """Strategic Benefits for SIU
• Levels playing field against extreme performers
• Rewards balanced excellence across indicators
• Reduces ranking volatility from outliers
• Supports multidisciplinary approach

Key Implications
• Focus on consistent performance across ALL metrics
• Avoid over-investment in single indicators
• Build comprehensive institutional strength
• Understand diminishing returns of extreme performance

Bottom Line
Damping supports SIU's balanced, multidisciplinary excellence strategy rather than narrow specialization."""
    
    right_frame.text = right_text
    
    # Format both columns
    for frame in [left_frame, right_frame]:
        for i, paragraph in enumerate(frame.paragraphs):
            if paragraph.text.endswith(('Definition & Purpose', 'How It Works', 'Example Impact', 
                                       'Strategic Benefits for SIU', 'Key Implications', 'Bottom Line')):
                paragraph.font.size = Pt(16)
                paragraph.font.bold = True
                paragraph.font.color.rgb = siu_blue
                paragraph.space_after = Pt(8)
            elif paragraph.text.startswith('•'):
                paragraph.font.size = Pt(13)
                paragraph.font.color.rgb = RGBColor(51, 51, 51)
                paragraph.level = 1
                paragraph.space_after = Pt(4)
            else:
                paragraph.font.size = Pt(13)
                paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # Save standalone presentation
    prs.save('QS_WUR_2026_Damping_Explanation.pptx')
    
    return prs

if __name__ == "__main__":
    print("Creating QS WUR damping explanation slides...")
    
    # Add damping slide to existing presentation
    try:
        prs1 = add_damping_slide_to_presentation()
        print("✓ Damping slide added to existing presentation: QS_WUR_2026_Analysis_SIU_With_Damping.pptx")
    except Exception as e:
        print(f"Note: Could not add to existing presentation: {e}")
    
    # Create standalone damping presentation
    prs2 = create_standalone_damping_presentation()
    print("✓ Standalone damping presentation created: QS_WUR_2026_Damping_Explanation.pptx")
    
    print("\nDamping explanation materials created:")
    print("• PowerPoint slides with clear explanations")
    print("• Strategic implications for SIU")
    print("• Easy-to-understand examples")
    print("• Professional presentation format")
    print("\n🎯 Damping explanation slides ready for Directors Meeting!")
