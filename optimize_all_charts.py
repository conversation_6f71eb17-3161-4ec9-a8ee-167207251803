"""
Optimize All QS WUR 2026 Charts for Better Space Utilization
Author: Dr<PERSON>, Symbiosis International (Deemed University)
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import seaborn as sns

def optimize_lens_distribution():
    """Create space-optimized lens distribution chart."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'research': '#1e40af',
        'employability': '#059669', 
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }
    
    # Optimized figure size
    fig, ax = plt.subplots(figsize=(12, 10))
    
    lenses = ['Research &\nDiscovery', 'Employability &\nOutcomes', 
              'Learning\nExperience', 'Global\nEngagement', 'Sustainability']
    weights = [50, 20, 10, 15, 5]
    lens_colors = [colors['research'], colors['employability'], colors['learning'], 
                   colors['global'], colors['sustainability']]
    
    # Create pie chart with optimal spacing
    wedges, texts, autotexts = ax.pie(weights, labels=lenses, autopct='%1.0f%%',
                                      colors=lens_colors, startangle=90, 
                                      textprops={'fontsize': 16, 'fontweight': 'bold'},
                                      explode=(0.05, 0.05, 0.05, 0.05, 0.05),
                                      pctdistance=0.85, labeldistance=1.1)
    
    # Optimized title positioning
    ax.set_title('QS World University Rankings 2026\nComplete Lens Distribution', 
                 fontsize=22, fontweight='bold', pad=15, color='#1e40af')
    
    # Enhanced text formatting
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(18)
    
    for text in texts:
        text.set_fontsize(14)
        text.set_fontweight('bold')
    
    # Bottom subtitle with minimal spacing
    fig.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University', 
             ha='center', fontsize=12, style='italic', color='#6b7280')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Lens_Distribution_Optimized.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def optimize_indicator_changes():
    """Create space-optimized indicator changes chart."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # Optimized figure size
    fig, ax = plt.subplots(figsize=(16, 10))
    
    indicators = ['Academic\nReputation', 'Citations per\nFaculty', 'Employer\nReputation', 
                 'Employment\nOutcomes', 'Faculty Student\nRatio', 'International\nFaculty', 
                 'International\nStudents', 'International\nResearch Network', 'Sustainability']
    
    weights_2024 = [40, 20, 10, 0, 20, 5, 5, 0, 0]
    weights_2026 = [30, 20, 15, 5, 10, 5, 5, 5, 5]
    changes = [w2026 - w2024 for w2024, w2026 in zip(weights_2024, weights_2026)]
    
    x = np.arange(len(indicators))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, weights_2024, width, label='2024 Weights', 
                   color='lightsteelblue', alpha=0.8, edgecolor='navy', linewidth=1.5)
    bars2 = ax.bar(x + width/2, weights_2026, width, label='2025/26 Weights', 
                   color='darkblue', alpha=0.9, edgecolor='navy', linewidth=1.5)
    
    # Enhanced value labels
    for bar in bars1:
        height = bar.get_height()
        if height > 0:
            ax.annotate(f'{height}%', xy=(bar.get_x() + bar.get_width()/2, height),
                        xytext=(0, 3), textcoords="offset points", ha='center', va='bottom',
                        fontsize=12, fontweight='bold')
    
    for bar in bars2:
        height = bar.get_height()
        if height > 0:
            ax.annotate(f'{height}%', xy=(bar.get_x() + bar.get_width()/2, height),
                        xytext=(0, 3), textcoords="offset points", ha='center', va='bottom',
                        fontsize=12, fontweight='bold')
    
    # Enhanced change indicators
    for i, change in enumerate(changes):
        if change > 0:
            ax.annotate(f'+{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 3),
                        ha='center', va='bottom', fontweight='bold', color='green', 
                        fontsize=13, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.8))
        elif change < 0:
            ax.annotate(f'{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 3),
                        ha='center', va='bottom', fontweight='bold', color='red', 
                        fontsize=13, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.8))
    
    # Optimized labels and title
    ax.set_xlabel('QS WUR 2026 Indicators', fontsize=16, fontweight='bold')
    ax.set_ylabel('Weight Percentage (%)', fontsize=16, fontweight='bold')
    ax.set_title('QS WUR 2026: Complete Indicator Weight Changes\n2024 vs 2025/26 Methodology', 
                 fontsize=20, fontweight='bold', pad=15, color='#1e40af')
    ax.set_xticks(x)
    ax.set_xticklabels(indicators, rotation=45, ha='right', fontsize=13)
    ax.legend(fontsize=14, loc='upper right')
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 50)
    
    # Bottom subtitle
    fig.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University', 
             ha='center', fontsize=12, style='italic', color='#6b7280')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Indicator_Changes_Optimized.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def optimize_lens_breakdown():
    """Create space-optimized lens breakdown chart."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'research': '#1e40af',
        'employability': '#059669', 
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }
    
    # Optimized figure size
    fig, ax = plt.subplots(figsize=(14, 10))
    
    lens_data = {
        'Research & Discovery (50%)': {'indicators': ['Academic Reputation (30%)', 'Citations per Faculty (20%)'], 
                                      'weights': [30, 20], 'color': colors['research']},
        'Employability & Outcomes (20%)': {'indicators': ['Employer Reputation (15%)', 'Employment Outcomes (5%)'], 
                                          'weights': [15, 5], 'color': colors['employability']},
        'Learning Experience (10%)': {'indicators': ['Faculty Student Ratio (10%)'], 
                                     'weights': [10], 'color': colors['learning']},
        'Global Engagement (15%)': {'indicators': ['International Faculty (5%)', 'International Students (5%)', 'International Research Network (5%)'], 
                                   'weights': [5, 5, 5], 'color': colors['global']},
        'Sustainability (5%)': {'indicators': ['Sustainability (5%)'], 
                               'weights': [5], 'color': colors['sustainability']}
    }
    
    y_positions = np.arange(len(lens_data))
    bar_height = 0.7
    
    for i, (lens, data) in enumerate(lens_data.items()):
        # Enhanced main lens bar
        ax.barh(y_positions[i], sum(data['weights']), height=bar_height, 
                color=data['color'], alpha=0.9, edgecolor='black', linewidth=2)
        
        # Enhanced lens label
        ax.text(sum(data['weights'])/2, y_positions[i], lens, 
                ha='center', va='center', fontweight='bold', fontsize=14, color='white')
        
        # Enhanced individual indicator bars
        x_offset = 0
        for j, (indicator, weight) in enumerate(zip(data['indicators'], data['weights'])):
            ax.barh(y_positions[i] - 0.3, weight, left=x_offset, height=0.2, 
                    color=data['color'], alpha=0.5, edgecolor='black', linewidth=1.5)
            
            # Enhanced indicator labels
            ax.text(x_offset + weight/2, y_positions[i] - 0.3, indicator, 
                    ha='center', va='center', fontsize=10, fontweight='bold')
            x_offset += weight
    
    ax.set_xlim(0, 55)
    ax.set_ylim(-0.6, len(lens_data) - 0.4)
    ax.set_xlabel('Weight Percentage (%)', fontsize=16, fontweight='bold')
    ax.set_title('QS WUR 2026: Lens-wise Indicator Breakdown\nHierarchical Structure of Methodology', 
                 fontsize=20, fontweight='bold', pad=15, color='#1e40af')
    ax.set_yticks([])
    ax.grid(True, alpha=0.3, axis='x')
    
    # Bottom subtitle
    fig.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University', 
             ha='center', fontsize=12, style='italic', color='#6b7280')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Lens_Breakdown_Optimized.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Optimizing all QS WUR 2026 methodology charts for better space utilization...")
    
    # Optimize all charts
    optimize_lens_distribution()
    print("✓ Lens distribution chart optimized")
    
    optimize_indicator_changes()
    print("✓ Indicator changes chart optimized")
    
    optimize_lens_breakdown()
    print("✓ Lens breakdown chart optimized")
    
    print("\nAll charts now feature:")
    print("• Minimal title-to-chart spacing")
    print("• Maximum use of available canvas space")
    print("• Enhanced readability and visual appeal")
    print("• Consistent professional styling")
    print("• Optimal font sizes and spacing")
    print("\n🎯 Complete set of space-optimized charts ready for presentation!")
