"""
Create Clean QS WUR 2026 Charts without Personal Branding
Author: Dr<PERSON>, Symbiosis International (Deemed University)
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import seaborn as sns

def create_clean_lens_distribution():
    """Create clean lens distribution chart without personal branding."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'research': '#1e40af',
        'employability': '#059669', 
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }
    
    # Optimized figure size
    fig, ax = plt.subplots(figsize=(12, 10))
    
    lenses = ['Research &\nDiscovery', 'Employability &\nOutcomes', 
              'Learning\nExperience', 'Global\nEngagement', 'Sustainability']
    weights = [50, 20, 10, 15, 5]
    lens_colors = [colors['research'], colors['employability'], colors['learning'], 
                   colors['global'], colors['sustainability']]
    
    # Create pie chart with optimal spacing
    wedges, texts, autotexts = ax.pie(weights, labels=lenses, autopct='%1.0f%%',
                                      colors=lens_colors, startangle=90, 
                                      textprops={'fontsize': 16, 'fontweight': 'bold'},
                                      explode=(0.05, 0.05, 0.05, 0.05, 0.05),
                                      pctdistance=0.85, labeldistance=1.1)
    
    # Clean title positioning
    ax.set_title('QS World University Rankings 2026\nComplete Lens Distribution', 
                 fontsize=22, fontweight='bold', pad=15, color='#1e40af')
    
    # Enhanced text formatting
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(18)
    
    for text in texts:
        text.set_fontsize(14)
        text.set_fontweight('bold')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Lens_Distribution_Clean.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_clean_indicator_changes():
    """Create clean indicator changes chart without personal branding."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # Optimized figure size
    fig, ax = plt.subplots(figsize=(16, 10))
    
    indicators = ['Academic\nReputation', 'Citations per\nFaculty', 'Employer\nReputation', 
                 'Employment\nOutcomes', 'Faculty Student\nRatio', 'International\nFaculty', 
                 'International\nStudents', 'International\nResearch Network', 'Sustainability']
    
    weights_2024 = [40, 20, 10, 0, 20, 5, 5, 0, 0]
    weights_2026 = [30, 20, 15, 5, 10, 5, 5, 5, 5]
    changes = [w2026 - w2024 for w2024, w2026 in zip(weights_2024, weights_2026)]
    
    x = np.arange(len(indicators))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, weights_2024, width, label='2024 Weights', 
                   color='lightsteelblue', alpha=0.8, edgecolor='navy', linewidth=1.5)
    bars2 = ax.bar(x + width/2, weights_2026, width, label='2025/26 Weights', 
                   color='darkblue', alpha=0.9, edgecolor='navy', linewidth=1.5)
    
    # Enhanced value labels
    for bar in bars1:
        height = bar.get_height()
        if height > 0:
            ax.annotate(f'{height}%', xy=(bar.get_x() + bar.get_width()/2, height),
                        xytext=(0, 3), textcoords="offset points", ha='center', va='bottom',
                        fontsize=12, fontweight='bold')
    
    for bar in bars2:
        height = bar.get_height()
        if height > 0:
            ax.annotate(f'{height}%', xy=(bar.get_x() + bar.get_width()/2, height),
                        xytext=(0, 3), textcoords="offset points", ha='center', va='bottom',
                        fontsize=12, fontweight='bold')
    
    # Enhanced change indicators
    for i, change in enumerate(changes):
        if change > 0:
            ax.annotate(f'+{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 3),
                        ha='center', va='bottom', fontweight='bold', color='green', 
                        fontsize=13, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.8))
        elif change < 0:
            ax.annotate(f'{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 3),
                        ha='center', va='bottom', fontweight='bold', color='red', 
                        fontsize=13, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.8))
    
    # Clean labels and title
    ax.set_xlabel('QS WUR 2026 Indicators', fontsize=16, fontweight='bold')
    ax.set_ylabel('Weight Percentage (%)', fontsize=16, fontweight='bold')
    ax.set_title('QS WUR 2026: Complete Indicator Weight Changes\n2024 vs 2025/26 Methodology', 
                 fontsize=20, fontweight='bold', pad=15, color='#1e40af')
    ax.set_xticks(x)
    ax.set_xticklabels(indicators, rotation=45, ha='right', fontsize=13)
    ax.legend(fontsize=14, loc='upper right')
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 50)
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Indicator_Changes_Clean.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_clean_lens_breakdown():
    """Create clean lens breakdown chart without personal branding."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'research': '#1e40af',
        'employability': '#059669', 
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }
    
    # Optimized figure size
    fig, ax = plt.subplots(figsize=(14, 10))
    
    lens_data = {
        'Research & Discovery (50%)': {'indicators': ['Academic Reputation (30%)', 'Citations per Faculty (20%)'], 
                                      'weights': [30, 20], 'color': colors['research']},
        'Employability & Outcomes (20%)': {'indicators': ['Employer Reputation (15%)', 'Employment Outcomes (5%)'], 
                                          'weights': [15, 5], 'color': colors['employability']},
        'Learning Experience (10%)': {'indicators': ['Faculty Student Ratio (10%)'], 
                                     'weights': [10], 'color': colors['learning']},
        'Global Engagement (15%)': {'indicators': ['International Faculty (5%)', 'International Students (5%)', 'International Research Network (5%)'], 
                                   'weights': [5, 5, 5], 'color': colors['global']},
        'Sustainability (5%)': {'indicators': ['Sustainability (5%)'], 
                               'weights': [5], 'color': colors['sustainability']}
    }
    
    y_positions = np.arange(len(lens_data))
    bar_height = 0.7
    
    for i, (lens, data) in enumerate(lens_data.items()):
        # Enhanced main lens bar
        ax.barh(y_positions[i], sum(data['weights']), height=bar_height, 
                color=data['color'], alpha=0.9, edgecolor='black', linewidth=2)
        
        # Enhanced lens label
        ax.text(sum(data['weights'])/2, y_positions[i], lens, 
                ha='center', va='center', fontweight='bold', fontsize=14, color='white')
        
        # Enhanced individual indicator bars
        x_offset = 0
        for j, (indicator, weight) in enumerate(zip(data['indicators'], data['weights'])):
            ax.barh(y_positions[i] - 0.3, weight, left=x_offset, height=0.2, 
                    color=data['color'], alpha=0.5, edgecolor='black', linewidth=1.5)
            
            # Enhanced indicator labels
            ax.text(x_offset + weight/2, y_positions[i] - 0.3, indicator, 
                    ha='center', va='center', fontsize=10, fontweight='bold')
            x_offset += weight
    
    ax.set_xlim(0, 55)
    ax.set_ylim(-0.6, len(lens_data) - 0.4)
    ax.set_xlabel('Weight Percentage (%)', fontsize=16, fontweight='bold')
    ax.set_title('QS WUR 2026: Lens-wise Indicator Breakdown\nHierarchical Structure of Methodology', 
                 fontsize=20, fontweight='bold', pad=15, color='#1e40af')
    ax.set_yticks([])
    ax.grid(True, alpha=0.3, axis='x')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Lens_Breakdown_Clean.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_clean_process_flow():
    """Create clean process flow chart without personal branding."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'research': '#1e40af',
        'employability': '#059669', 
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }
    
    # Create figure with optimized proportions
    fig, ax = plt.subplots(figsize=(14, 16))
    
    process_steps = [
        {
            "title": "1. DATA COLLECTION",
            "details": [
                "Academic Reputation Survey (60,000+ academics)",
                "Employer Reputation Survey (40,000+ employers)", 
                "Scopus Citation Database Analysis",
                "Institutional Data Submissions",
                "Alumni Employment Tracking"
            ]
        },
        {
            "title": "2. RAW DATA PROCESSING", 
            "details": [
                "Calculate Faculty-Student Ratios",
                "Compute International Staff/Student Percentages",
                "Apply Capping & Damping Mechanisms",
                "Data Quality Validation & Cleaning",
                "Missing Data Imputation"
            ]
        },
        {
            "title": "3. Z-SCORE NORMALIZATION",
            "details": [
                "Standardization: (X - μ) / σ",
                "Locked Means/Standard Deviations",
                "Top X Institution Benchmarking",
                "Normal Distribution Mapping",
                "Outlier Adjustment"
            ]
        },
        {
            "title": "4. MIN-MAX SCALING",
            "details": [
                "Convert Z-scores to 0-1 Range",
                "Apply 1-100 Final Scaling",
                "Weight Individual Indicators",
                "Calculate Lens Scores",
                "Combine Weighted Indicators"
            ]
        },
        {
            "title": "5. FINAL RANKING",
            "details": [
                "Sum All Weighted Lens Scores",
                "Apply Final Min-Max Normalization",
                "Assign Global Rank Positions",
                "Quality Assurance Checks",
                "Publish Final Rankings"
            ]
        }
    ]
    
    step_colors = [colors['research'], colors['employability'], colors['learning'], 
                   colors['global'], colors['sustainability']]
    
    # Optimized spacing - use more of the available space
    chart_start_y = 0.95  # Start higher
    chart_end_y = 0.08    # End lower
    total_chart_height = chart_start_y - chart_end_y
    step_spacing = total_chart_height / len(process_steps)
    
    for i, step in enumerate(process_steps):
        # Calculate positions with better space utilization
        y_top = chart_start_y - (i * step_spacing)
        y_bottom = y_top - (step_spacing * 0.85)  # Use 85% of available space
        
        # Larger main step box
        main_box_height = 0.06
        main_box_y = y_top - main_box_height
        
        main_box = patches.FancyBboxPatch(
            (0.08, main_box_y), 0.84, main_box_height,
            boxstyle="round,pad=0.01", 
            facecolor=step_colors[i], 
            alpha=0.9, 
            edgecolor='black', 
            linewidth=2
        )
        ax.add_patch(main_box)
        
        # Step title with larger font
        ax.text(0.5, main_box_y + main_box_height/2, step["title"], 
                ha='center', va='center', 
                fontsize=18, fontweight='bold', color='white', 
                transform=ax.transAxes)
        
        # Detail boxes with optimized spacing
        detail_box_height = 0.032
        detail_spacing = 0.006
        details_start_y = main_box_y - 0.015
        
        for j, detail in enumerate(step["details"]):
            detail_y = details_start_y - (j * (detail_box_height + detail_spacing))
            
            # Wider detail background box
            detail_box = patches.FancyBboxPatch(
                (0.12, detail_y), 0.76, detail_box_height,
                boxstyle="round,pad=0.005", 
                facecolor=step_colors[i], 
                alpha=0.25, 
                edgecolor=step_colors[i],
                linewidth=1.5
            )
            ax.add_patch(detail_box)
            
            # Detail text with better formatting
            ax.text(0.5, detail_y + detail_box_height/2, f"• {detail}", 
                    ha='center', va='center', 
                    fontsize=12, fontweight='normal', 
                    color='black',
                    transform=ax.transAxes)
        
        # Improved arrow to next step
        if i < len(process_steps) - 1:
            arrow_start_y = y_bottom + 0.015
            arrow_end_y = arrow_start_y - 0.025
            
            ax.annotate('', 
                       xy=(0.5, arrow_end_y), 
                       xytext=(0.5, arrow_start_y),
                       arrowprops=dict(
                           arrowstyle='->', 
                           lw=5, 
                           color='darkblue',
                           alpha=0.9
                       ),
                       transform=ax.transAxes)
    
    # Clean title positioning
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.text(0.5, 0.99, 'QS World University Rankings 2026', 
            ha='center', va='top', fontsize=22, fontweight='bold', 
            transform=ax.transAxes, color='#1e40af')
    ax.text(0.5, 0.97, 'Complete Methodology Process Flow', 
            ha='center', va='top', fontsize=18, fontweight='bold', 
            transform=ax.transAxes, color='#374151')
    ax.axis('off')
    
    # Enhanced side annotations
    ax.text(0.02, 0.5, 'DATA\nINPUTS', ha='center', va='center', 
            fontsize=16, fontweight='bold', rotation=90, 
            transform=ax.transAxes, color='#1e40af',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.3))
    
    ax.text(0.98, 0.5, 'RANKING\nOUTPUTS', ha='center', va='center', 
            fontsize=16, fontweight='bold', rotation=90, 
            transform=ax.transAxes, color='#1e40af',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.3))
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Process_Flow_Clean.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_clean_strategic_impact():
    """Create clean strategic impact chart without personal branding."""

    plt.style.use('seaborn-v0_8-whitegrid')

    # Colors
    siu_blue = '#1e40af'
    siu_light_blue = '#3b82f6'
    siu_green = '#10b981'
    siu_orange = '#f59e0b'
    siu_red = '#ef4444'

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

    # 1. Impact Assessment Matrix
    impact_areas = ['Employment\nOutcomes', 'International\nResearch', 'Sustainability',
                   'Academic\nReputation', 'Faculty\nRatio']
    current_performance = [65, 70, 45, 80, 85]
    improvement_potential = [90, 85, 80, 85, 75]

    x = np.arange(len(impact_areas))
    width = 0.35

    bars1 = ax1.bar(x - width/2, current_performance, width, label='Current Performance',
                   color=siu_orange, alpha=0.7)
    bars2 = ax1.bar(x + width/2, improvement_potential, width, label='Improvement Potential',
                   color=siu_green, alpha=0.8)

    ax1.set_title('SIU Performance vs. Improvement Potential\nKey QS WUR Indicators',
                 fontsize=16, fontweight='bold')
    ax1.set_ylabel('Performance Score (%)', fontsize=12, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(impact_areas)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 100)

    # 2. Competitive Positioning
    universities = ['IIT Delhi\n(123)', 'IIT Madras\n(180)', 'SIU\n(696)', 'Target\n(500)', 'Target\n(400)']
    ranks = [123, 180, 696, 500, 400]
    colors = [siu_green, siu_green, siu_red, siu_orange, siu_blue]

    bars = ax2.barh(universities, [1000-r for r in ranks], color=colors, alpha=0.8)
    ax2.set_title('SIU Competitive Positioning\nQS WUR 2026 Rankings',
                 fontsize=16, fontweight='bold')
    ax2.set_xlabel('Ranking Position (Higher is Better)', fontsize=12, fontweight='bold')

    # 3. Strategic Action Timeline
    actions = ['Employment\nTracking', 'International\nPartnerships', 'Sustainability\nPrograms',
              'Research\nCollaboration', 'Quality\nExpansion']
    timeline = [6, 12, 18, 24, 36]
    impact_level = [85, 90, 75, 95, 80]

    scatter = ax3.scatter(timeline, impact_level, s=[200, 250, 180, 300, 220],
                         c=[siu_green, siu_blue, siu_orange, siu_light_blue, siu_red], alpha=0.7)

    for i, action in enumerate(actions):
        ax3.annotate(action, (timeline[i], impact_level[i]), xytext=(5, 5),
                    textcoords='offset points', fontsize=10, fontweight='bold')

    ax3.set_title('Strategic Action Timeline\nImplementation Roadmap',
                 fontsize=16, fontweight='bold')
    ax3.set_xlabel('Timeline (Months)', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Expected Impact Level', fontsize=12, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.set_xlim(0, 40)
    ax3.set_ylim(70, 100)

    # 4. Resource Allocation Pie Chart
    resources = ['Research Enhancement', 'International Partnerships', 'Career Services',
                'Sustainability Initiatives', 'Infrastructure Development']
    allocation = [30, 25, 20, 15, 10]
    colors_pie = [siu_blue, siu_green, siu_light_blue, siu_orange, siu_red]

    wedges, texts, autotexts = ax4.pie(allocation, labels=resources, autopct='%1.1f%%',
                                      colors=colors_pie, startangle=90)
    ax4.set_title('Recommended Resource Allocation\nStrategic Investment Priorities',
                 fontsize=16, fontweight='bold')

    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(11)

    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Strategic_Impact_Clean.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Creating clean QS WUR 2026 methodology charts without personal branding...")

    # Create all clean charts
    create_clean_lens_distribution()
    print("✓ Clean lens distribution chart created")

    create_clean_indicator_changes()
    print("✓ Clean indicator changes chart created")

    create_clean_lens_breakdown()
    print("✓ Clean lens breakdown chart created")

    create_clean_process_flow()
    print("✓ Clean process flow chart created")

    create_clean_strategic_impact()
    print("✓ Clean strategic impact chart created")

    print("\nAll clean charts created:")
    print("• QS_WUR_2026_Lens_Distribution_Clean.png")
    print("• QS_WUR_2026_Indicator_Changes_Clean.png")
    print("• QS_WUR_2026_Lens_Breakdown_Clean.png")
    print("• QS_WUR_2026_Process_Flow_Clean.png")
    print("• QS_WUR_2026_Strategic_Impact_Clean.png")
    print("\nFeatures:")
    print("• No personal branding or affiliation")
    print("• Optimal space utilization")
    print("• Professional presentation quality")
    print("• Ready for institutional use")
    print("\n🎯 Clean charts ready for your Directors Meeting!")
