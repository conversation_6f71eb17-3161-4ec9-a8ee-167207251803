"""
Complete PowerPoint Presentation Generator for QS WUR 2026 Analysis
Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
from pptx.enum.dml import MSO_THEME_COLOR
import os

def create_complete_presentation():
    """Create a comprehensive PowerPoint presentation for QS WUR 2026 analysis."""
    
    # Create presentation
    prs = Presentation()
    
    # Set slide dimensions (16:9 aspect ratio)
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)
    
    # Define SIU brand colors
    siu_blue = RGBColor(30, 64, 175)
    siu_light_blue = RGBColor(59, 130, 246)
    siu_green = RGBColor(16, 185, 129)
    siu_orange = RGBColor(245, 158, 11)
    siu_red = RGBColor(239, 68, 68)
    gray_text = RGBColor(107, 114, 128)
    
    # Slide 1: Title Slide
    slide1_layout = prs.slide_layouts[0]
    slide1 = prs.slides.add_slide(slide1_layout)
    
    title1 = slide1.shapes.title
    subtitle1 = slide1.placeholders[1]
    
    title1.text = "QS World University Rankings 2026"
    title1.text_frame.paragraphs[0].font.size = Pt(48)
    title1.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title1.text_frame.paragraphs[0].font.bold = True
    title1.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    
    subtitle_text = """Strategic Analysis & Recommendations for 
Symbiosis International University

Presented by:
Dr. Dharmendra Pandey
Deputy Director - Quality Management & Benchmarking (QMB)
Head - Quality Assurance (QA)
Symbiosis International (Deemed University)

Directors Meeting - June 2025"""
    
    subtitle1.text = subtitle_text
    for i, paragraph in enumerate(subtitle1.text_frame.paragraphs):
        if i == 0:
            paragraph.font.size = Pt(24)
            paragraph.font.color.rgb = siu_light_blue
            paragraph.font.bold = True
        elif i in [2, 3, 4, 5]:
            paragraph.font.size = Pt(18)
            paragraph.font.color.rgb = siu_blue
            if i == 3:
                paragraph.font.bold = True
        else:
            paragraph.font.size = Pt(16)
            paragraph.font.color.rgb = gray_text
        paragraph.alignment = PP_ALIGN.CENTER
    
    # Slide 2: Introduction to QS WUR 2026
    slide2_layout = prs.slide_layouts[1]
    slide2 = prs.slides.add_slide(slide2_layout)
    
    title2 = slide2.shapes.title
    title2.text = "Introduction to QS World University Rankings 2026"
    title2.text_frame.paragraphs[0].font.size = Pt(36)
    title2.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title2.text_frame.paragraphs[0].font.bold = True
    
    # Add content placeholder
    content_box = slide2.shapes.add_textbox(Inches(0.5), Inches(1.5), Inches(12), Inches(5.5))
    content_frame = content_box.text_frame
    content_frame.word_wrap = True
    
    content_text = """What is QS World University Rankings?
• Annual global university ranking by Quacquarelli Symonds (QS)
• Evaluates 1,500+ universities across 105 higher education systems
• Uses sophisticated multi-layered methodology with "lenses," "indicators," and "metrics"
• Highly regarded benchmark for institutional reputation and student decision-making

India's Remarkable Trajectory in QS WUR
• 54 Indian universities ranked in 2026 (compared to only 11 in 2015) - 5x growth
• India is now the 4th most represented country globally
• 8 new Indian entrants in 2026 - highest number globally
• 48% of Indian institutions improved their positions year-over-year

SIU's Current Global Standing
• Rank 696 globally in QS WUR 2026 (slight decline from 641-650 in 2025)
• Remains among top 1000 universities worldwide
• Reinforces Pune's status as significant educational hub alongside SPPU
• Part of India's positive national trajectory in global higher education"""
    
    content_frame.text = content_text
    
    # Format content
    for i, paragraph in enumerate(content_frame.paragraphs):
        if paragraph.text.endswith('?') or paragraph.text.endswith('WUR') or paragraph.text.endswith('Standing'):
            paragraph.font.size = Pt(20)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_blue
            paragraph.space_after = Pt(12)
        else:
            paragraph.font.size = Pt(16)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            if paragraph.text.startswith('•'):
                paragraph.level = 1
                paragraph.space_after = Pt(6)
    
    # Slide 3: Methodology Changes
    slide3_layout = prs.slide_layouts[1]
    slide3 = prs.slides.add_slide(slide3_layout)
    
    title3 = slide3.shapes.title
    title3.text = "QS WUR 2026 Methodology: Key Changes & Strategic Implications"
    title3.text_frame.paragraphs[0].font.size = Pt(32)
    title3.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title3.text_frame.paragraphs[0].font.bold = True
    
    # Create table for methodology changes
    rows, cols = 7, 5
    table_left = Inches(0.5)
    table_top = Inches(1.8)
    table_width = Inches(12)
    table_height = Inches(4.5)
    
    table = slide3.shapes.add_table(rows, cols, table_left, table_top, table_width, table_height).table
    
    # Set column widths
    table.columns[0].width = Inches(3)
    table.columns[1].width = Inches(1.5)
    table.columns[2].width = Inches(1.5)
    table.columns[3].width = Inches(1.5)
    table.columns[4].width = Inches(4.5)
    
    # Header row
    headers = ['Indicator', '2024 Weight', '2025/26 Weight', 'Change', 'Strategic Impact']
    for i, header in enumerate(headers):
        cell = table.cell(0, i)
        cell.text = header
        cell.text_frame.paragraphs[0].font.bold = True
        cell.text_frame.paragraphs[0].font.size = Pt(14)
        cell.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)
        cell.fill.solid()
        cell.fill.fore_color.rgb = siu_blue
    
    # Data rows
    data = [
        ['Academic Reputation', '40%', '30%', '-10%', 'Reduced emphasis on traditional prestige'],
        ['Faculty Student Ratio', '20%', '10%', '-10%', 'Less weight on input metrics'],
        ['Employer Reputation', '10%', '15%', '+5%', 'Greater focus on graduate outcomes'],
        ['Employment Outcomes', '0%', '5%', '+5%', 'NEW: Alumni impact & employment rates'],
        ['International Research Network', '0%', '5%', '+5%', 'NEW: Global research collaboration'],
        ['Sustainability', '0%', '5%', '+5%', 'NEW: Environmental & social impact']
    ]
    
    for i, row_data in enumerate(data, 1):
        for j, cell_data in enumerate(row_data):
            cell = table.cell(i, j)
            cell.text = cell_data
            cell.text_frame.paragraphs[0].font.size = Pt(12)
            
            # Color coding for changes
            if j == 3:  # Change column
                if cell_data.startswith('+'):
                    cell.text_frame.paragraphs[0].font.color.rgb = siu_green
                    cell.text_frame.paragraphs[0].font.bold = True
                elif cell_data.startswith('-'):
                    cell.text_frame.paragraphs[0].font.color.rgb = siu_red
                    cell.text_frame.paragraphs[0].font.bold = True
    
    # Add key insight box
    insight_box = slide3.shapes.add_textbox(Inches(1), Inches(6.5), Inches(11), Inches(0.8))
    insight_frame = insight_box.text_frame
    insight_frame.text = "Key Insight: The 2026 methodology represents a paradigm shift from input-based to outcome-based evaluation, emphasizing real-world impact, global collaboration, and institutional responsibility."
    insight_frame.paragraphs[0].font.size = Pt(16)
    insight_frame.paragraphs[0].font.bold = True
    insight_frame.paragraphs[0].font.color.rgb = siu_orange
    
    return prs

def add_remaining_slides(prs):
    """Add the remaining slides to complete the presentation."""
    
    siu_blue = RGBColor(30, 64, 175)
    siu_light_blue = RGBColor(59, 130, 246)
    siu_green = RGBColor(16, 185, 129)
    siu_orange = RGBColor(245, 158, 11)
    siu_red = RGBColor(239, 68, 68)
    gray_text = RGBColor(107, 114, 128)
    
    # Slide 4: Impact on SIU & Strategic Recommendations
    slide4_layout = prs.slide_layouts[1]
    slide4 = prs.slides.add_slide(slide4_layout)
    
    title4 = slide4.shapes.title
    title4.text = "Impact on SIU & Strategic Recommendations"
    title4.text_frame.paragraphs[0].font.size = Pt(36)
    title4.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title4.text_frame.paragraphs[0].font.bold = True
    
    # Create three-column layout for impact assessment
    col_width = Inches(3.8)
    col_height = Inches(5)
    
    # Strengths column
    strengths_box = slide4.shapes.add_textbox(Inches(0.5), Inches(1.8), col_width, col_height)
    strengths_frame = strengths_box.text_frame
    strengths_text = """✓ STRENGTHS TO LEVERAGE

• Very High Research Output
• Top-10 Management, Law, Media institutes
• 40,000+ students from 85+ countries
• New Medical College expansion
• Strong multidisciplinary profile
• Industry alignment capabilities"""
    
    strengths_frame.text = strengths_text
    strengths_frame.paragraphs[0].font.size = Pt(16)
    strengths_frame.paragraphs[0].font.bold = True
    strengths_frame.paragraphs[0].font.color.rgb = siu_green
    
    for i in range(1, len(strengths_frame.paragraphs)):
        strengths_frame.paragraphs[i].font.size = Pt(14)
        strengths_frame.paragraphs[i].font.color.rgb = RGBColor(51, 51, 51)
    
    # Opportunities column
    opportunities_box = slide4.shapes.add_textbox(Inches(4.7), Inches(1.8), col_width, col_height)
    opportunities_frame = opportunities_box.text_frame
    opportunities_text = """⚠ AREAS FOR IMPROVEMENT

• International Research Networks
• Employment Outcome tracking
• Sustainability initiatives
• Citation impact enhancement
• Global faculty diversity
• Alumni impact measurement"""
    
    opportunities_frame.text = opportunities_text
    opportunities_frame.paragraphs[0].font.size = Pt(16)
    opportunities_frame.paragraphs[0].font.bold = True
    opportunities_frame.paragraphs[0].font.color.rgb = siu_orange
    
    for i in range(1, len(opportunities_frame.paragraphs)):
        opportunities_frame.paragraphs[i].font.size = Pt(14)
        opportunities_frame.paragraphs[i].font.color.rgb = RGBColor(51, 51, 51)
    
    # Challenges column
    challenges_box = slide4.shapes.add_textbox(Inches(8.9), Inches(1.8), col_width, col_height)
    challenges_frame = challenges_box.text_frame
    challenges_text = """⚡ IMMEDIATE CHALLENGES

• Rank decline (641-650 → 696)
• Increased competition bar
• Need for comprehensive excellence
• Resource allocation across disciplines
• Normalization benchmark effects
• Maintaining quality during expansion"""
    
    challenges_frame.text = challenges_text
    challenges_frame.paragraphs[0].font.size = Pt(16)
    challenges_frame.paragraphs[0].font.bold = True
    challenges_frame.paragraphs[0].font.color.rgb = siu_red
    
    for i in range(1, len(challenges_frame.paragraphs)):
        challenges_frame.paragraphs[i].font.size = Pt(14)
        challenges_frame.paragraphs[i].font.color.rgb = RGBColor(51, 51, 51)
    
    return prs

# Generate the complete presentation
if __name__ == "__main__":
    print("Creating complete PowerPoint presentation...")
    
    # Create the presentation
    prs = create_complete_presentation()
    prs = add_remaining_slides(prs)
    
    # Save the presentation
    prs.save('QS_WUR_2026_Analysis_SIU_Complete.pptx')
    
    print("✓ Complete PowerPoint presentation created: QS_WUR_2026_Analysis_SIU_Complete.pptx")
    print("\nPresentation includes:")
    print("1. Title slide with SIU branding")
    print("2. Introduction to QS WUR 2026")
    print("3. Methodology changes with detailed table")
    print("4. Impact assessment and strategic recommendations")
    print("\nReady for Directors Meeting presentation!")
