"""
Create Simple, Clean Damping Explanation Charts
Author: Dr<PERSON>, Symbiosis International (Deemed University)
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.patches import FancyBboxPatch

def create_simple_damping_explanation():
    """Create a simple, clean damping explanation chart."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # Colors
    colors = {
        'primary': '#1e40af',
        'secondary': '#3b82f6',
        'accent': '#10b981',
        'warning': '#f59e0b',
        'danger': '#ef4444'
    }
    
    # Create single figure with better control
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('QS WUR Damping: Simple Explanation for Directors', 
                 fontsize=18, fontweight='bold', y=0.95, color=colors['primary'])
    
    # 1. What is Damping?
    ax1.text(0.5, 0.85, 'What is Damping?', ha='center', va='center', 
            fontsize=14, fontweight='bold', transform=ax1.transAxes, color=colors['primary'])
    
    ax1.text(0.5, 0.6, 'Statistical technique that reduces\nextreme outlier values to ensure\nfair university comparisons', 
            ha='center', va='center', fontsize=12, fontweight='normal', 
            transform=ax1.transAxes, color='black',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.3))
    
    ax1.text(0.5, 0.3, 'Purpose: Prevent single universities\nfrom dominating rankings through\nextreme performance in one metric', 
            ha='center', va='center', fontsize=11, fontweight='normal', 
            transform=ax1.transAxes, color='black',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.5))
    
    # 2. Simple Example
    ax2.set_title('Before vs After Damping Example', fontsize=14, fontweight='bold', color=colors['primary'])
    
    universities = ['Normal Uni', 'SIU', 'Extreme Uni']
    before = [75, 35, 180]
    after = [70, 34, 65]
    
    x = np.arange(len(universities))
    width = 0.35
    
    bars1 = ax2.bar(x - width/2, before, width, label='Before Damping', 
                   color=colors['danger'], alpha=0.7)
    bars2 = ax2.bar(x + width/2, after, width, label='After Damping', 
                   color=colors['accent'], alpha=0.8)
    
    # Add value labels
    for i, (b, a) in enumerate(zip(before, after)):
        ax2.text(i - width/2, b + 2, str(b), ha='center', va='bottom', fontweight='bold', fontsize=10)
        ax2.text(i + width/2, a + 2, str(a), ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    ax2.set_ylabel('Citations per Faculty', fontsize=11, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(universities, fontsize=10)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 200)
    
    # 3. Benefits for SIU
    ax3.text(0.5, 0.9, 'Benefits for SIU', ha='center', va='center', 
            fontsize=14, fontweight='bold', transform=ax3.transAxes, color=colors['primary'])
    
    benefits = [
        "Levels playing field",
        "Rewards balanced excellence", 
        "Reduces ranking volatility",
        "Supports multidisciplinary approach"
    ]
    
    for i, benefit in enumerate(benefits):
        y_pos = 0.7 - (i * 0.15)
        ax3.text(0.1, y_pos, f"• {benefit}", ha='left', va='center', 
                fontsize=11, fontweight='normal', transform=ax3.transAxes, color='black')
    
    # 4. Strategic Implications
    ax4.text(0.5, 0.9, 'Strategic Implications', ha='center', va='center', 
            fontsize=14, fontweight='bold', transform=ax4.transAxes, color=colors['primary'])
    
    implications = [
        "Focus on ALL indicators",
        "Avoid single-metric focus",
        "Build comprehensive excellence",
        "Leverage balanced approach"
    ]
    
    for i, implication in enumerate(implications):
        y_pos = 0.7 - (i * 0.15)
        ax4.text(0.1, y_pos, f"{i+1}. {implication}", ha='left', va='center',
                fontsize=11, fontweight='bold', transform=ax4.transAxes, color='black')
    
    # Key takeaway
    ax4.text(0.5, 0.1, 'KEY: Damping favors SIU\'s balanced\nmultidisciplinary strategy', 
            ha='center', va='center', fontsize=11, fontweight='bold', 
            transform=ax4.transAxes, color=colors['accent'],
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.3))
    
    # Clean up all axes
    for ax in [ax1, ax3, ax4]:
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Damping_Simple.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_damping_process_simple():
    """Create a simple damping process flow."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'primary': '#1e40af',
        'secondary': '#3b82f6',
        'accent': '#10b981',
        'warning': '#f59e0b',
        'danger': '#ef4444'
    }
    
    fig, ax = plt.subplots(figsize=(14, 10))
    
    ax.text(0.5, 0.95, 'QS WUR Damping Process: Simple Steps', 
            ha='center', va='top', fontsize=18, fontweight='bold', 
            transform=ax.transAxes, color=colors['primary'])
    
    # Simple 5-step process
    steps = [
        {"title": "1. Collect Data", "desc": "Citations, ratios, surveys", "example": "SIU: 35 citations/faculty"},
        {"title": "2. Find Extremes", "desc": "Identify outlier values", "example": "Extreme Uni: 180 citations/faculty"},
        {"title": "3. Apply Damping", "desc": "Reduce extreme values", "example": "180 → 65 (damped)"},
        {"title": "4. Normalize", "desc": "Convert to 1-100 scale", "example": "SIU: 34 → Score 45/100"},
        {"title": "5. Final Rank", "desc": "Combine all indicators", "example": "SIU: Rank 696"}
    ]
    
    step_colors = [colors['secondary'], colors['warning'], colors['danger'], colors['accent'], colors['primary']]
    
    for i, step in enumerate(steps):
        y_pos = 0.8 - (i * 0.15)
        
        # Step number circle
        circle = plt.Circle((0.08, y_pos), 0.03, facecolor=step_colors[i], alpha=0.9, 
                           edgecolor='black', linewidth=2, transform=ax.transAxes)
        ax.add_patch(circle)
        
        ax.text(0.08, y_pos, str(i+1), ha='center', va='center', 
                fontsize=12, fontweight='bold', color='white', transform=ax.transAxes)
        
        # Step title
        ax.text(0.15, y_pos + 0.02, step["title"], ha='left', va='center', 
                fontsize=13, fontweight='bold', color=step_colors[i], transform=ax.transAxes)
        
        # Description
        ax.text(0.15, y_pos - 0.02, step["desc"], ha='left', va='center', 
                fontsize=11, fontweight='normal', color='black', transform=ax.transAxes)
        
        # Example
        ax.text(0.6, y_pos, step["example"], ha='left', va='center', 
                fontsize=11, fontweight='bold', color='black', transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.2", facecolor='lightgray', alpha=0.5))
        
        # Arrow to next step
        if i < len(steps) - 1:
            ax.annotate('', xy=(0.08, y_pos - 0.08), xytext=(0.08, y_pos - 0.05),
                       arrowprops=dict(arrowstyle='->', lw=2, color='black'),
                       transform=ax.transAxes)
    
    # Key result
    ax.text(0.5, 0.05, 'RESULT: Fairer rankings that benefit balanced institutions like SIU', 
            ha='center', va='center', fontsize=13, fontweight='bold', 
            transform=ax.transAxes, color=colors['primary'],
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.5))
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Damping_Process_Simple.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Creating simple, clean damping explanation charts...")
    
    # Create simple damping explanation
    create_simple_damping_explanation()
    print("✓ Simple damping explanation created: QS_WUR_2026_Damping_Simple.png")
    
    # Create simple process flow
    create_damping_process_simple()
    print("✓ Simple damping process created: QS_WUR_2026_Damping_Process_Simple.png")
    
    print("\nSimple damping charts created:")
    print("• Clean 4-panel explanation with proper text fitting")
    print("• Simple step-by-step process flow")
    print("• No text overflow or cluttered appearance")
    print("• Professional format for directors")
    print("\n🎯 Clean, simple damping explanation ready!")
