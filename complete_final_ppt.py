"""
Final PowerPoint Presentation with Strategic Action Plan
Author: Dr. <PERSON><PERSON><PERSON>, Symbiosis International (Deemed University)
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor

def add_strategic_action_slide():
    """Add the final strategic action plan slide."""
    
    # Load existing presentation
    prs = Presentation('QS_WUR_2026_Analysis_SIU_Complete.pptx')
    
    # Define colors
    siu_blue = RGBColor(30, 64, 175)
    siu_light_blue = RGBColor(59, 130, 246)
    siu_green = RGBColor(16, 185, 129)
    siu_orange = RGBColor(245, 158, 11)
    
    # Slide 5: Strategic Action Plan
    slide5_layout = prs.slide_layouts[1]
    slide5 = prs.slides.add_slide(slide5_layout)
    
    title5 = slide5.shapes.title
    title5.text = "Strategic Action Plan for SIU: QS WUR Enhancement"
    title5.text_frame.paragraphs[0].font.size = Pt(32)
    title5.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title5.text_frame.paragraphs[0].font.bold = True
    
    # Create action plan content
    action_box = slide5.shapes.add_textbox(Inches(0.5), Inches(1.8), Inches(12), Inches(5))
    action_frame = action_box.text_frame
    action_frame.word_wrap = True
    
    action_text = """IMMEDIATE ACTIONS (6-12 months)
1. Employment Outcomes Enhancement
   • Implement comprehensive graduate tracking system
   • Strengthen career services and industry partnerships
   • Document alumni leadership impact and success stories

2. International Research Network Development
   • Target 3+ joint papers with global institutions over 5 years
   • Establish MOUs with top-ranked international universities
   • Create international research collaboration incentives

MEDIUM-TERM INITIATIVES (12-24 months)
3. Sustainability Integration
   • Develop comprehensive ESG policies and reporting
   • Align research programs with UN Sustainable Development Goals
   • Showcase alumni contributions to climate solutions

4. Research Excellence & Citation Impact
   • Enhance research infrastructure and faculty development
   • Focus on high-impact publication venues
   • Strengthen research mentorship and collaboration programs

LONG-TERM STRATEGIC POSITIONING (24-36 months)
5. Quality Expansion Management
   • Ensure new disciplines (medical sciences) achieve research excellence
   • Maintain concentrated investment in high-potential areas
   • Leverage subject-specific rankings in Law, Management, Media, Liberal Arts

Expected Outcome: Target rank improvement to 500-600 range by 2028"""
    
    action_frame.text = action_text
    
    # Format the content
    for i, paragraph in enumerate(action_frame.paragraphs):
        if 'IMMEDIATE' in paragraph.text or 'MEDIUM-TERM' in paragraph.text or 'LONG-TERM' in paragraph.text:
            paragraph.font.size = Pt(18)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_blue
            paragraph.space_after = Pt(12)
        elif paragraph.text.startswith(('1.', '2.', '3.', '4.', '5.')):
            paragraph.font.size = Pt(16)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_green
            paragraph.space_after = Pt(8)
        elif paragraph.text.startswith('Expected Outcome'):
            paragraph.font.size = Pt(16)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_orange
            paragraph.space_after = Pt(8)
        else:
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            if paragraph.text.startswith('   •'):
                paragraph.level = 1
                paragraph.space_after = Pt(4)
    
    # Slide 6: Conclusion & Next Steps
    slide6_layout = prs.slide_layouts[1]
    slide6 = prs.slides.add_slide(slide6_layout)
    
    title6 = slide6.shapes.title
    title6.text = "Conclusion & Next Steps"
    title6.text_frame.paragraphs[0].font.size = Pt(36)
    title6.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title6.text_frame.paragraphs[0].font.bold = True
    
    # Create conclusion content
    conclusion_box = slide6.shapes.add_textbox(Inches(0.5), Inches(1.8), Inches(12), Inches(4.5))
    conclusion_frame = conclusion_box.text_frame
    conclusion_frame.word_wrap = True
    
    conclusion_text = """Key Takeaways
• QS WUR 2026 methodology shift favors outcome-based evaluation over traditional metrics
• India's rising trajectory provides favorable context for SIU's improvement efforts
• SIU's multidisciplinary expansion aligns with QS's preference for comprehensive universities
• Strategic focus on employment outcomes, international collaboration, and sustainability is critical

Immediate Board Decisions Required
• Approval for enhanced graduate tracking and career services investment
• Authorization for international research partnership development
• Resource allocation for sustainability initiatives and ESG reporting
• Support for faculty development and research infrastructure enhancement

Success Metrics & Timeline
• Target: Improve to 500-600 rank range by QS WUR 2028
• Quarterly progress reviews on key indicators
• Annual assessment of strategic initiative effectiveness
• Integration with SIU's overall strategic planning process

Thank you for your attention. Questions and discussion welcome."""
    
    conclusion_frame.text = conclusion_text
    
    # Format conclusion content
    for i, paragraph in enumerate(conclusion_frame.paragraphs):
        if paragraph.text in ['Key Takeaways', 'Immediate Board Decisions Required', 'Success Metrics & Timeline']:
            paragraph.font.size = Pt(20)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_blue
            paragraph.space_after = Pt(12)
        elif paragraph.text.startswith('Thank you'):
            paragraph.font.size = Pt(18)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_green
            paragraph.alignment = PP_ALIGN.CENTER
            paragraph.space_before = Pt(20)
        else:
            paragraph.font.size = Pt(16)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            if paragraph.text.startswith('•'):
                paragraph.level = 1
                paragraph.space_after = Pt(6)
    
    # Save the complete presentation
    prs.save('QS_WUR_2026_Analysis_SIU_Final.pptx')
    
    return prs

if __name__ == "__main__":
    print("Adding final slides to complete the presentation...")
    
    prs = add_strategic_action_slide()
    
    print("✓ Final PowerPoint presentation created: QS_WUR_2026_Analysis_SIU_Final.pptx")
    print("\nComplete presentation now includes:")
    print("1. Title slide with SIU branding")
    print("2. Introduction to QS WUR 2026")
    print("3. Methodology changes with detailed analysis")
    print("4. Impact assessment and strategic recommendations")
    print("5. Strategic action plan with timeline")
    print("6. Conclusion and next steps")
    print("\n🎯 Ready for Directors Meeting presentation!")
    print("📊 Supporting visualizations also available as PNG files")
