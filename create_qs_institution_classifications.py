"""
QS Institution Classifications - Professional Visualization Generator
Author: Dr. <PERSON><PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-27

This script generates professional PowerPoint-friendly tables and charts for 
QS Institution Classifications across four dimensions: Size, Subject Range, 
Age (discontinued), and Research Intensity.
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
from matplotlib.colors import LinearSegmentedColormap
import warnings
warnings.filterwarnings('ignore')

# Professional styling setup
def setup_professional_style():
    """Set up professional styling for all visualizations."""
    plt.style.use('default')
    
    # Professional color palette
    colors = {
        'primary': '#2E86AB',      # Professional blue
        'secondary': '#A23B72',    # Deep magenta
        'accent': '#F18F01',       # Orange
        'success': '#C73E1D',      # Red
        'neutral': '#6C757D',      # Gray
        'light': '#F8F9FA',        # Light gray
        'dark': '#212529'          # Dark gray
    }
    
    # Typography settings
    plt.rcParams.update({
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.titleweight': 'bold',
        'axes.labelsize': 14,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12,
        'legend.fontsize': 11,
        'figure.titlesize': 18,
        'figure.titleweight': 'bold',
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.grid': True,
        'grid.alpha': 0.3,
        'figure.facecolor': 'white',
        'axes.facecolor': 'white'
    })
    
    return colors

def create_size_classification_table():
    """Create professional table for Size Classification."""
    colors = setup_professional_style()
    
    # Data for size classification
    size_data = {
        'Code': ['XL', 'L', 'M', 'S'],
        'Classification': ['Extra Large', 'Large', 'Medium', 'Small'],
        'Student Count': ['More than 30,000', '≥12,000', '≥5,000', 'Fewer than 5,000'],
        'Description': [
            'Major research universities with extensive programs',
            'Large comprehensive institutions',
            'Mid-sized universities with diverse offerings',
            'Smaller specialized or focused institutions'
        ]
    }
    
    df = pd.DataFrame(size_data)
    
    # Create figure
    fig, ax = plt.subplots(figsize=(14, 8))
    ax.axis('tight')
    ax.axis('off')
    
    # Create table
    table = ax.table(cellText=df.values,
                    colLabels=df.columns,
                    cellLoc='left',
                    loc='center',
                    colWidths=[0.08, 0.2, 0.2, 0.52])
    
    # Style the table
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1, 2.5)
    
    # Header styling
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor(colors['primary'])
        table[(0, i)].set_text_props(weight='bold', color='white')
        table[(0, i)].set_height(0.15)
    
    # Row styling with alternating colors
    row_colors = [colors['light'], 'white']
    for i in range(1, len(df) + 1):
        for j in range(len(df.columns)):
            table[(i, j)].set_facecolor(row_colors[i % 2])
            table[(i, j)].set_height(0.12)
            if j == 0:  # Code column - make bold
                table[(i, j)].set_text_props(weight='bold', color=colors['primary'])
    
    plt.title('QS Institution Classifications: Size Categories\n(Based on Full-Time Equivalent Student Body)', 
              fontsize=18, fontweight='bold', pad=30, color=colors['dark'])
    
    plt.tight_layout()
    plt.savefig('QS_Size_Classification_Table.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ Size Classification Table created: QS_Size_Classification_Table.png")

def create_subject_range_classification():
    """Create professional visualization for Subject Range Classification."""
    colors = setup_professional_style()
    
    # Data for subject range
    subject_data = {
        'Code': ['FC', 'CO', 'FO', 'SP'],
        'Classification': ['Full Comprehensive', 'Comprehensive', 'Focused', 'Specialist'],
        'Faculty Areas': ['All 5 + Medical School', 'All 5 Faculty Areas', '3-4 Faculty Areas', '≤2 Faculty Areas'],
        'Characteristics': [
            'Complete university with medical programs',
            'Full range of academic disciplines',
            'Strong in selected academic areas',
            'Concentrated expertise in specific fields'
        ]
    }
    
    df = pd.DataFrame(subject_data)
    
    # Create figure
    fig, ax = plt.subplots(figsize=(14, 8))
    ax.axis('tight')
    ax.axis('off')
    
    # Create table
    table = ax.table(cellText=df.values,
                    colLabels=df.columns,
                    cellLoc='left',
                    loc='center',
                    colWidths=[0.08, 0.25, 0.25, 0.42])
    
    # Style the table
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1, 2.5)
    
    # Header styling
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor(colors['secondary'])
        table[(0, i)].set_text_props(weight='bold', color='white')
        table[(0, i)].set_height(0.15)
    
    # Row styling
    row_colors = [colors['light'], 'white']
    for i in range(1, len(df) + 1):
        for j in range(len(df.columns)):
            table[(i, j)].set_facecolor(row_colors[i % 2])
            table[(i, j)].set_height(0.12)
            if j == 0:  # Code column
                table[(i, j)].set_text_props(weight='bold', color=colors['secondary'])
    
    plt.title('QS Institution Classifications: Subject Range Categories\n(Based on Faculty Area Coverage)', 
              fontsize=18, fontweight='bold', pad=30, color=colors['dark'])
    
    plt.tight_layout()
    plt.savefig('QS_Subject_Range_Classification_Table.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ Subject Range Classification Table created: QS_Subject_Range_Classification_Table.png")

def create_age_classification_discontinued():
    """Create visualization showing Age Classification (discontinued since 2023)."""
    colors = setup_professional_style()
    
    # Age classification data
    age_data = {
        'Level': [5, 4, 3, 2, 1],
        'Classification': ['Historic', 'Mature', 'Established', 'Young', 'New'],
        'Age Range': ['≥100 years', '50-99 years', '25-49 years', '10-24 years', '<10 years'],
        'Status': ['DISCONTINUED', 'DISCONTINUED', 'DISCONTINUED', 'DISCONTINUED', 'DISCONTINUED']
    }
    
    df = pd.DataFrame(age_data)
    
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 7))
    ax.axis('tight')
    ax.axis('off')
    
    # Create table
    table = ax.table(cellText=df.values,
                    colLabels=df.columns,
                    cellLoc='center',
                    loc='center',
                    colWidths=[0.12, 0.25, 0.25, 0.25])
    
    # Style the table
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1, 2.2)
    
    # Header styling
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor(colors['neutral'])
        table[(0, i)].set_text_props(weight='bold', color='white')
        table[(0, i)].set_height(0.15)
    
    # Row styling - all grayed out since discontinued
    for i in range(1, len(df) + 1):
        for j in range(len(df.columns)):
            table[(i, j)].set_facecolor('#F5F5F5')
            table[(i, j)].set_height(0.12)
            if j == 3:  # Status column
                table[(i, j)].set_text_props(weight='bold', color=colors['success'])
            else:
                table[(i, j)].set_text_props(color=colors['neutral'])
    
    plt.title('QS Institution Classifications: Age Categories\n(DISCONTINUED since 2023 Rankings Cycle)', 
              fontsize=18, fontweight='bold', pad=30, color=colors['success'])
    
    # Add disclaimer
    plt.figtext(0.5, 0.02, 'Note: Age classification is no longer used in QS World University Rankings since 2023',
                ha='center', fontsize=11, style='italic', color=colors['success'])
    
    plt.tight_layout()
    plt.savefig('QS_Age_Classification_Discontinued.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ Age Classification (Discontinued) created: QS_Age_Classification_Discontinued.png")

def create_research_intensity_matrix():
    """Create comprehensive Research Intensity classification matrix."""
    colors = setup_professional_style()

    # Research intensity thresholds data
    research_data = {
        'Research Focus': ['FC', 'FC', 'FC', 'FC', 'CO', 'CO', 'CO', 'CO', 'FO', 'FO', 'FO', 'FO', 'SP', 'SP', 'SP', 'SP'],
        'Intensity': ['VH', 'HI', 'MD', 'LO', 'VH', 'HI', 'MD', 'LO', 'VH', 'HI', 'MD', 'LO', 'VH', 'HI', 'MD', 'LO'],
        'XL (>30k)': [13000, 4000, 750, 0, 7000, 2000, 400, 0, 3500, 1000, 150, 0, '2x mean*', '1x mean*', '0.5x mean*', 0],
        'L (≥12k)': [10000, 3000, 500, 0, 5000, 1500, 250, 0, 2500, 750, 100, 0, '2x mean*', '1x mean*', '0.5x mean*', 0],
        'M (≥5k)': [5000, 1500, 250, 0, 2500, 750, 100, 0, 1250, 400, 50, 0, '2x mean*', '1x mean*', '0.5x mean*', 0],
        'S (<5k)': [2500, 750, 100, 0, 1250, 400, 50, 0, 650, 200, 50, 0, '2x mean*', '1x mean*', '0.5x mean*', 0]
    }

    df = pd.DataFrame(research_data)

    # Create figure with larger size for complex matrix
    fig, ax = plt.subplots(figsize=(16, 12))
    ax.axis('tight')
    ax.axis('off')

    # Create table
    table = ax.table(cellText=df.values,
                    colLabels=df.columns,
                    cellLoc='center',
                    loc='center',
                    colWidths=[0.15, 0.12, 0.18, 0.18, 0.18, 0.18])

    # Style the table
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 1.8)

    # Header styling
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor(colors['accent'])
        table[(0, i)].set_text_props(weight='bold', color='white')
        table[(0, i)].set_height(0.12)

    # Color coding for different focus types
    focus_colors = {
        'FC': colors['primary'],
        'CO': colors['secondary'],
        'FO': colors['accent'],
        'SP': colors['success']
    }

    # Row styling with focus-based coloring
    for i in range(1, len(df) + 1):
        focus_type = df.iloc[i-1]['Research Focus']
        intensity = df.iloc[i-1]['Intensity']

        for j in range(len(df.columns)):
            # Light background based on focus type
            if focus_type in focus_colors:
                alpha = 0.1 if intensity in ['VH', 'HI'] else 0.05
                table[(i, j)].set_facecolor(focus_colors[focus_type])
                table[(i, j)].set_alpha(alpha)

            table[(i, j)].set_height(0.08)

            # Bold formatting for focus and intensity columns
            if j in [0, 1]:
                table[(i, j)].set_text_props(weight='bold', color=focus_colors.get(focus_type, colors['dark']))

    plt.title('QS Institution Classifications: Research Intensity Matrix\n(Scopus Documents Required - 5-Year Period)',
              fontsize=18, fontweight='bold', pad=40, color=colors['dark'])

    # Add legend
    legend_elements = [
        mpatches.Patch(color=colors['primary'], alpha=0.3, label='FC - Full Comprehensive'),
        mpatches.Patch(color=colors['secondary'], alpha=0.3, label='CO - Comprehensive'),
        mpatches.Patch(color=colors['accent'], alpha=0.3, label='FO - Focused'),
        mpatches.Patch(color=colors['success'], alpha=0.3, label='SP - Specialist')
    ]

    ax.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, -0.05),
              ncol=4, frameon=False, fontsize=11)

    # Add footnote
    plt.figtext(0.5, 0.02, '*SP thresholds calculated as multiples of mean for relevant broad subject areas',
                ha='center', fontsize=10, style='italic', color=colors['neutral'])

    plt.tight_layout()
    plt.savefig('QS_Research_Intensity_Matrix.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

    print("✓ Research Intensity Matrix created: QS_Research_Intensity_Matrix.png")

def create_research_intensity_explanation():
    """Create explanation chart for Research Intensity levels."""
    colors = setup_professional_style()

    # Research intensity explanations
    intensity_data = {
        'Level': ['VH', 'HI', 'MD', 'LO'],
        'Classification': ['Very High', 'High', 'Medium', 'Low'],
        'Description': [
            'Leading research institutions with extensive publication output',
            'Strong research universities with significant scholarly activity',
            'Moderate research activity with selective focus areas',
            'Limited research output or teaching-focused institutions'
        ],
        'Evaluation Basis': [
            'Scopus documents in 5-year period (highest thresholds)',
            'Scopus documents in 5-year period (high thresholds)',
            'Scopus documents in 5-year period (moderate thresholds)',
            'Scopus documents in 5-year period (minimal/no threshold)'
        ]
    }

    df = pd.DataFrame(intensity_data)

    # Create figure
    fig, ax = plt.subplots(figsize=(16, 8))
    ax.axis('tight')
    ax.axis('off')

    # Create table
    table = ax.table(cellText=df.values,
                    colLabels=df.columns,
                    cellLoc='left',
                    loc='center',
                    colWidths=[0.08, 0.15, 0.35, 0.42])

    # Style the table
    table.auto_set_font_size(False)
    table.set_fontsize(11)
    table.scale(1, 2.8)

    # Header styling
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor(colors['accent'])
        table[(0, i)].set_text_props(weight='bold', color='white')
        table[(0, i)].set_height(0.15)

    # Row styling with intensity-based coloring
    intensity_colors = ['#1a5490', '#2e86ab', '#a23b72', '#c73e1d']  # Gradient from blue to red

    for i in range(1, len(df) + 1):
        for j in range(len(df.columns)):
            table[(i, j)].set_facecolor('#f8f9fa' if i % 2 == 0 else 'white')
            table[(i, j)].set_height(0.12)

            if j == 0:  # Level column
                table[(i, j)].set_text_props(weight='bold', color=intensity_colors[i-1])
                table[(i, j)].set_facecolor(intensity_colors[i-1])
                table[(i, j)].set_text_props(weight='bold', color='white')

    plt.title('QS Institution Classifications: Research Intensity Levels\n(Detailed Explanation)',
              fontsize=18, fontweight='bold', pad=30, color=colors['dark'])

    plt.tight_layout()
    plt.savefig('QS_Research_Intensity_Explanation.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

    print("✓ Research Intensity Explanation created: QS_Research_Intensity_Explanation.png")

def create_classification_summary_overview():
    """Create comprehensive overview of all QS Institution Classifications."""
    colors = setup_professional_style()

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
    fig.suptitle('QS Institution Classifications: Complete Framework Overview',
                 fontsize=20, fontweight='bold', y=0.95)

    # 1. Size Classification (Top Left)
    ax1.axis('off')
    size_data = [['XL', 'Extra Large', '>30,000'], ['L', 'Large', '≥12,000'],
                 ['M', 'Medium', '≥5,000'], ['S', 'Small', '<5,000']]

    table1 = ax1.table(cellText=size_data,
                      colLabels=['Code', 'Classification', 'Students'],
                      cellLoc='center', loc='center',
                      colWidths=[0.2, 0.4, 0.4])
    table1.auto_set_font_size(False)
    table1.set_fontsize(10)
    table1.scale(1, 2)

    # Style table 1
    for i in range(3):
        table1[(0, i)].set_facecolor(colors['primary'])
        table1[(0, i)].set_text_props(weight='bold', color='white')

    ax1.set_title('Size Classification', fontsize=14, fontweight='bold', pad=20)

    # 2. Subject Range Classification (Top Right)
    ax2.axis('off')
    subject_data = [['FC', 'Full Comprehensive', '5 + Medical'], ['CO', 'Comprehensive', '5 Areas'],
                    ['FO', 'Focused', '3-4 Areas'], ['SP', 'Specialist', '≤2 Areas']]

    table2 = ax2.table(cellText=subject_data,
                      colLabels=['Code', 'Classification', 'Faculty Areas'],
                      cellLoc='center', loc='center',
                      colWidths=[0.2, 0.4, 0.4])
    table2.auto_set_font_size(False)
    table2.set_fontsize(10)
    table2.scale(1, 2)

    # Style table 2
    for i in range(3):
        table2[(0, i)].set_facecolor(colors['secondary'])
        table2[(0, i)].set_text_props(weight='bold', color='white')

    ax2.set_title('Subject Range Classification', fontsize=14, fontweight='bold', pad=20)

    # 3. Age Classification - Discontinued (Bottom Left)
    ax3.axis('off')
    age_data = [['5', 'Historic', '≥100 years', 'DISCONTINUED'],
                ['4', 'Mature', '50-99 years', 'DISCONTINUED'],
                ['3', 'Established', '25-49 years', 'DISCONTINUED'],
                ['2', 'Young', '10-24 years', 'DISCONTINUED'],
                ['1', 'New', '<10 years', 'DISCONTINUED']]

    table3 = ax3.table(cellText=age_data,
                      colLabels=['Level', 'Classification', 'Age', 'Status'],
                      cellLoc='center', loc='center',
                      colWidths=[0.15, 0.25, 0.25, 0.35])
    table3.auto_set_font_size(False)
    table3.set_fontsize(9)
    table3.scale(1, 1.5)

    # Style table 3 - grayed out
    for i in range(4):
        table3[(0, i)].set_facecolor(colors['neutral'])
        table3[(0, i)].set_text_props(weight='bold', color='white')

    for i in range(1, 6):
        for j in range(4):
            table3[(i, j)].set_facecolor('#f5f5f5')
            if j == 3:  # Status column
                table3[(i, j)].set_text_props(weight='bold', color=colors['success'])

    ax3.set_title('Age Classification (Discontinued 2023)', fontsize=14, fontweight='bold', pad=20, color=colors['success'])

    # 4. Research Intensity Overview (Bottom Right)
    ax4.axis('off')
    research_summary = [['VH', 'Very High', 'Leading Research'], ['HI', 'High', 'Strong Research'],
                       ['MD', 'Medium', 'Moderate Research'], ['LO', 'Low', 'Limited Research']]

    table4 = ax4.table(cellText=research_summary,
                      colLabels=['Level', 'Classification', 'Description'],
                      cellLoc='center', loc='center',
                      colWidths=[0.2, 0.3, 0.5])
    table4.auto_set_font_size(False)
    table4.set_fontsize(10)
    table4.scale(1, 2)

    # Style table 4
    for i in range(3):
        table4[(0, i)].set_facecolor(colors['accent'])
        table4[(0, i)].set_text_props(weight='bold', color='white')

    # Color code research levels
    research_colors = ['#1a5490', '#2e86ab', '#a23b72', '#c73e1d']
    for i in range(1, 5):
        table4[(i, 0)].set_facecolor(research_colors[i-1])
        table4[(i, 0)].set_text_props(weight='bold', color='white')

    ax4.set_title('Research Intensity Classification', fontsize=14, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.subplots_adjust(top=0.92, hspace=0.3, wspace=0.2)
    plt.savefig('QS_Institution_Classifications_Complete_Overview.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

    print("✓ Complete Classifications Overview created: QS_Institution_Classifications_Complete_Overview.png")

if __name__ == "__main__":
    print("Creating QS Institution Classifications Visualizations...")
    print("=" * 60)

    # Create all classification tables
    create_size_classification_table()
    create_subject_range_classification()
    create_age_classification_discontinued()
    create_research_intensity_matrix()
    create_research_intensity_explanation()
    create_classification_summary_overview()

    print("=" * 60)
    print("All QS Institution Classification visualizations created successfully!")
    print("\nFiles generated:")
    print("1. QS_Size_Classification_Table.png")
    print("2. QS_Subject_Range_Classification_Table.png")
    print("3. QS_Age_Classification_Discontinued.png")
    print("4. QS_Research_Intensity_Matrix.png")
    print("5. QS_Research_Intensity_Explanation.png")
    print("6. QS_Institution_Classifications_Complete_Overview.png")
    print("\n" + "=" * 60)
    print("All visualizations are PowerPoint-friendly and ready for professional presentations!")
    print("Each chart is optimized for clarity and professional appearance.")
