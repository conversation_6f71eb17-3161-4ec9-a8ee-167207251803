"""
QS Institution Classifications - Ultra Clean Flowchart
Author: Dr. <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-27

This script creates the cleanest, most professional flowchart for QS Institution Classifications
with perfect alignment, minimal clutter, and maximum clarity.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

def setup_ultra_clean_style():
    """Set up ultra-clean professional styling."""
    plt.style.use('default')
    
    colors = {
        'primary': '#1E3A8A',      # Deep blue
        'secondary': '#7C3AED',    # Purple  
        'accent': '#F59E0B',       # Amber
        'success': '#DC2626',      # Red
        'neutral': '#374151',      # Gray
        'light': '#F9FAFB',        # Very light gray
        'dark': '#111827'          # Very dark gray
    }
    
    plt.rcParams.update({
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans'],
        'font.size': 11,
        'axes.titlesize': 18,
        'axes.titleweight': 'bold',
        'figure.titlesize': 20,
        'figure.titleweight': 'bold',
        'figure.facecolor': 'white',
        'axes.facecolor': 'white'
    })
    
    return colors

def create_ultra_clean_flowchart():
    """Create the cleanest possible flowchart with perfect alignment."""
    colors = setup_ultra_clean_style()
    
    fig, ax = plt.subplots(figsize=(20, 10))
    ax.set_xlim(0, 20)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    def add_perfect_box(x, y, width, height, text, color, text_color='white', fontsize=12, bold=True):
        """Add perfectly aligned box with clean styling."""
        box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                           boxstyle="round,pad=0.3", 
                           facecolor=color, 
                           edgecolor='white',
                           linewidth=3)
        ax.add_patch(box)
        
        weight = 'bold' if bold else 'normal'
        ax.text(x, y, text, ha='center', va='center', fontsize=fontsize, 
                color=text_color, weight=weight)
    
    def add_perfect_arrow(x1, y1, x2, y2, color=None):
        """Add perfectly aligned arrow."""
        if color is None:
            color = colors['dark']
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle='->', lw=4, color=color, 
                                 connectionstyle="arc3,rad=0"))
    
    # Main title area
    add_perfect_box(10, 8.5, 8, 1.2, 'QS INSTITUTION CLASSIFICATION FRAMEWORK', 
                   colors['primary'], fontsize=16)
    
    # Three main classification steps - perfectly spaced
    step_y = 6.5
    add_perfect_box(4, step_y, 3, 1.4, 'STEP 1\nSIZE\n(Student Body)', colors['secondary'], fontsize=13)
    add_perfect_box(10, step_y, 3, 1.4, 'STEP 2\nSUBJECT RANGE\n(Faculty Areas)', colors['accent'], fontsize=13)
    add_perfect_box(16, step_y, 3, 1.4, 'STEP 3\nRESEARCH\n(Publications)', colors['success'], fontsize=13)
    
    # Arrows from title to steps
    add_perfect_arrow(8, 7.9, 5, 7.2)
    add_perfect_arrow(10, 7.9, 10, 7.2)
    add_perfect_arrow(12, 7.9, 15, 7.2)
    
    # Classification results - perfectly aligned
    result_y = 4.5
    
    # Size results
    add_perfect_box(2.5, result_y, 1.8, 0.9, 'XL\n>30k', colors['secondary'], fontsize=11)
    add_perfect_box(4, result_y-1, 1.8, 0.9, 'L\n≥12k', colors['secondary'], fontsize=11)
    add_perfect_box(5.5, result_y, 1.8, 0.9, 'M\n≥5k', colors['secondary'], fontsize=11)
    add_perfect_box(4, result_y-2, 1.8, 0.9, 'S\n<5k', colors['secondary'], fontsize=11)
    
    # Subject results
    add_perfect_box(8.5, result_y, 1.8, 0.9, 'FC\n5+Med', colors['accent'], fontsize=11)
    add_perfect_box(10, result_y-1, 1.8, 0.9, 'CO\n5 Areas', colors['accent'], fontsize=11)
    add_perfect_box(11.5, result_y, 1.8, 0.9, 'FO\n3-4', colors['accent'], fontsize=11)
    add_perfect_box(10, result_y-2, 1.8, 0.9, 'SP\n≤2', colors['accent'], fontsize=11)
    
    # Research results
    add_perfect_box(14.5, result_y, 1.8, 0.9, 'VH\nVery High', colors['success'], fontsize=11)
    add_perfect_box(16, result_y-1, 1.8, 0.9, 'HI\nHigh', colors['success'], fontsize=11)
    add_perfect_box(17.5, result_y, 1.8, 0.9, 'MD\nMedium', colors['success'], fontsize=11)
    add_perfect_box(16, result_y-2, 1.8, 0.9, 'LO\nLow', colors['success'], fontsize=11)
    
    # Arrows from steps to results
    add_perfect_arrow(4, 5.8, 4, 5.2)
    add_perfect_arrow(10, 5.8, 10, 5.2)
    add_perfect_arrow(16, 5.8, 16, 5.2)
    
    # Final classification box
    add_perfect_box(10, 1.5, 6, 1.2, 'FINAL CLASSIFICATION\n(Example: L-CO-HI)', 
                   colors['primary'], fontsize=14)
    
    # Clean convergence arrows to final result
    add_perfect_arrow(4, 2.5, 8, 2)
    add_perfect_arrow(10, 2.5, 10, 2.2)
    add_perfect_arrow(16, 2.5, 12, 2)
    
    # Add clean legend at bottom
    legend_y = 0.3
    ax.text(4, legend_y, 'SIZE CATEGORIES', ha='center', fontsize=10, 
            color=colors['secondary'], weight='bold')
    ax.text(10, legend_y, 'SUBJECT FOCUS', ha='center', fontsize=10, 
            color=colors['accent'], weight='bold')
    ax.text(16, legend_y, 'RESEARCH INTENSITY', ha='center', fontsize=10, 
            color=colors['success'], weight='bold')
    
    plt.title('QS Institution Classification: Ultra-Clean Process Flow\n' + 
              'Professional Framework for University Categorization', 
              fontsize=22, fontweight='bold', pad=40, color=colors['dark'])
    
    plt.tight_layout()
    plt.savefig('QS_Classification_Ultra_Clean_Flowchart.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none', pad_inches=0.5)
    plt.close()
    
    print("✓ Ultra-Clean Flowchart created: QS_Classification_Ultra_Clean_Flowchart.png")

def create_minimalist_process_diagram():
    """Create ultra-minimalist process diagram."""
    colors = setup_ultra_clean_style()
    
    fig, ax = plt.subplots(figsize=(16, 6))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 6)
    ax.axis('off')
    
    # Simple process boxes
    boxes = [
        (2, 3, 'INSTITUTION\nDATA', colors['neutral']),
        (5, 3, 'SIZE\nANALYSIS', colors['secondary']),
        (8, 3, 'SUBJECT\nRANGE', colors['accent']),
        (11, 3, 'RESEARCH\nINTENSITY', colors['success']),
        (14, 3, 'FINAL\nCODE', colors['primary'])
    ]
    
    for x, y, text, color in boxes:
        # Clean rectangular boxes
        rect = patches.Rectangle((x-0.8, y-0.6), 1.6, 1.2, 
                               facecolor=color, edgecolor='white', linewidth=3)
        ax.add_patch(rect)
        ax.text(x, y, text, ha='center', va='center', fontsize=12, 
                color='white', weight='bold')
    
    # Simple arrows between boxes
    arrow_positions = [(3, 3, 4, 3), (6, 3, 7, 3), (9, 3, 10, 3), (12, 3, 13, 3)]
    for x1, y1, x2, y2 in arrow_positions:
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle='->', lw=5, color=colors['dark']))
    
    # Clean result indicators
    results = [
        (5, 1.5, 'XL•L•M•S', colors['secondary']),
        (8, 1.5, 'FC•CO•FO•SP', colors['accent']),
        (11, 1.5, 'VH•HI•MD•LO', colors['success'])
    ]
    
    for x, y, text, color in results:
        ax.text(x, y, text, ha='center', va='center', fontsize=11, 
                color=color, weight='bold')
        # Simple line connecting to main box
        ax.plot([x, x], [y+0.3, 2.4], color=color, linewidth=2, alpha=0.7)
    
    plt.title('QS Classification: Minimalist Process Overview', 
              fontsize=18, fontweight='bold', pad=30, color=colors['dark'])
    
    plt.tight_layout()
    plt.savefig('QS_Classification_Minimalist_Process.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ Minimalist Process Diagram created: QS_Classification_Minimalist_Process.png")

if __name__ == "__main__":
    print("Creating Ultra-Clean QS Classification Flowcharts...")
    print("=" * 60)
    
    create_ultra_clean_flowchart()
    create_minimalist_process_diagram()
    
    print("=" * 60)
    print("Ultra-clean flowcharts created successfully!")
    print("\nFiles generated:")
    print("1. QS_Classification_Ultra_Clean_Flowchart.png")
    print("2. QS_Classification_Minimalist_Process.png")
    print("\nFeatures:")
    print("• Perfect alignment and spacing")
    print("• No overlapping elements")
    print("• Clean, professional arrows")
    print("• Minimal visual clutter")
    print("• Maximum clarity and readability")
    print("• PowerPoint-ready formatting")
