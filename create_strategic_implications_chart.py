"""
QS Institution Classifications - Strategic Implications Visualization
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-27

This script creates strategic implications and practical considerations
charts for QS Institution Classifications.
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches
import warnings
warnings.filterwarnings('ignore')

def setup_professional_style():
    """Set up professional styling for all visualizations."""
    plt.style.use('default')
    
    colors = {
        'primary': '#2E86AB',
        'secondary': '#A23B72', 
        'accent': '#F18F01',
        'success': '#C73E1D',
        'neutral': '#6C757D',
        'light': '#F8F9FA',
        'dark': '#212529'
    }
    
    plt.rcParams.update({
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'Deja<PERSON>u Sans', 'Liberation Sans'],
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.titleweight': 'bold',
        'axes.labelsize': 14,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12,
        'legend.fontsize': 11,
        'figure.titlesize': 18,
        'figure.titleweight': 'bold',
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.grid': True,
        'grid.alpha': 0.3,
        'figure.facecolor': 'white',
        'axes.facecolor': 'white'
    })
    
    return colors

def create_strategic_implications_matrix():
    """Create strategic implications matrix for different classification combinations."""
    colors = setup_professional_style()
    
    fig, ax = plt.subplots(figsize=(16, 12))
    
    # Strategic implications data
    implications_data = {
        'Size + Focus': [
            'XL + FC', 'XL + CO', 'XL + FO', 'XL + SP',
            'L + FC', 'L + CO', 'L + FO', 'L + SP',
            'M + FC', 'M + CO', 'M + FO', 'M + SP',
            'S + FC', 'S + CO', 'S + FO', 'S + SP'
        ],
        'Research Threshold': [
            'Very High', 'High', 'High', 'Subject-based',
            'High', 'Moderate', 'Moderate', 'Subject-based',
            'Moderate', 'Low', 'Low', 'Subject-based',
            'Low', 'Very Low', 'Very Low', 'Subject-based'
        ],
        'Strategic Priority': [
            'Global Research Leadership', 'Comprehensive Excellence', 'Focused Excellence', 'Niche Leadership',
            'Regional Research Hub', 'Broad Academic Strength', 'Selective Excellence', 'Specialized Excellence',
            'Balanced Development', 'Academic Diversity', 'Strategic Focus', 'Deep Specialization',
            'Community Impact', 'Local Comprehensiveness', 'Targeted Strength', 'Unique Expertise'
        ],
        'Key Challenges': [
            'Maintaining breadth & depth', 'Resource allocation', 'Depth vs breadth', 'Market positioning',
            'Scaling research output', 'Quality consistency', 'Resource concentration', 'Visibility & impact',
            'Growth vs quality', 'Program sustainability', 'Competitive advantage', 'Market differentiation',
            'Resource constraints', 'Program viability', 'Faculty recruitment', 'Student attraction'
        ]
    }
    
    df = pd.DataFrame(implications_data)
    
    ax.axis('tight')
    ax.axis('off')
    
    # Create table
    table = ax.table(cellText=df.values,
                    colLabels=df.columns,
                    cellLoc='left',
                    loc='center',
                    colWidths=[0.15, 0.2, 0.35, 0.3])
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 1.8)
    
    # Header styling
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor(colors['primary'])
        table[(0, i)].set_text_props(weight='bold', color='white')
        table[(0, i)].set_height(0.12)
    
    # Row styling with size-based coloring
    size_colors = {
        'XL': colors['primary'],
        'L': colors['secondary'],
        'M': colors['accent'], 
        'S': colors['success']
    }
    
    for i in range(1, len(df) + 1):
        size_code = df.iloc[i-1]['Size + Focus'].split(' + ')[0]
        base_color = size_colors.get(size_code, colors['neutral'])
        
        for j in range(len(df.columns)):
            # Light background based on size
            table[(i, j)].set_facecolor(base_color)
            table[(i, j)].set_alpha(0.1)
            table[(i, j)].set_height(0.08)
            
            if j == 0:  # Size + Focus column
                table[(i, j)].set_text_props(weight='bold', color=base_color)
                table[(i, j)].set_alpha(0.3)
    
    plt.title('QS Institution Classifications: Strategic Implications Matrix\n(Classification Combinations and Strategic Considerations)', 
              fontsize=18, fontweight='bold', pad=40, color=colors['dark'])
    
    # Add legend
    legend_elements = [
        mpatches.Patch(color=colors['primary'], alpha=0.3, label='XL - Extra Large (>30k)'),
        mpatches.Patch(color=colors['secondary'], alpha=0.3, label='L - Large (≥12k)'),
        mpatches.Patch(color=colors['accent'], alpha=0.3, label='M - Medium (≥5k)'),
        mpatches.Patch(color=colors['success'], alpha=0.3, label='S - Small (<5k)')
    ]
    
    ax.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, -0.05), 
              ncol=4, frameon=False, fontsize=11)
    
    plt.tight_layout()
    plt.savefig('QS_Classifications_Strategic_Implications_Matrix.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ Strategic Implications Matrix created: QS_Classifications_Strategic_Implications_Matrix.png")

def create_classification_decision_flowchart():
    """Create decision flowchart for understanding classification process."""
    colors = setup_professional_style()
    
    fig, ax = plt.subplots(figsize=(14, 10))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # Define flowchart elements
    def add_box(x, y, width, height, text, color, text_color='white'):
        box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                           boxstyle="round,pad=0.1", 
                           facecolor=color, edgecolor='black', linewidth=1.5)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=10, 
                color=text_color, weight='bold', wrap=True)
    
    def add_arrow(x1, y1, x2, y2):
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle='->', lw=2, color=colors['dark']))
    
    # Start
    add_box(5, 9, 2, 0.8, 'Institution\nClassification\nProcess', colors['primary'])
    
    # Size Classification
    add_box(2, 7.5, 1.8, 0.8, 'Size\nClassification\n(Student FTE)', colors['secondary'])
    add_arrow(4.2, 8.6, 2.8, 8)
    
    # Subject Range
    add_box(5, 7.5, 1.8, 0.8, 'Subject Range\n(Faculty Areas)', colors['accent'])
    add_arrow(5, 8.6, 5, 8)
    
    # Research Intensity
    add_box(8, 7.5, 1.8, 0.8, 'Research Intensity\n(Scopus Docs)', colors['success'])
    add_arrow(5.8, 8.6, 7.2, 8)
    
    # Size outcomes
    add_box(1, 6, 1.2, 0.6, 'XL: >30k', colors['secondary'], 'white')
    add_box(2, 6, 1.2, 0.6, 'L: ≥12k', colors['secondary'], 'white')
    add_box(3, 6, 1.2, 0.6, 'M: ≥5k', colors['secondary'], 'white')
    add_box(1.5, 5.2, 1.2, 0.6, 'S: <5k', colors['secondary'], 'white')
    
    add_arrow(2, 7.1, 1.5, 6.5)
    
    # Subject outcomes
    add_box(4, 6, 1.2, 0.6, 'FC: 5+Med', colors['accent'], 'white')
    add_box(5, 6, 1.2, 0.6, 'CO: 5 Areas', colors['accent'], 'white')
    add_box(6, 6, 1.2, 0.6, 'FO: 3-4', colors['accent'], 'white')
    add_box(5, 5.2, 1.2, 0.6, 'SP: ≤2', colors['accent'], 'white')
    
    add_arrow(5, 7.1, 5, 6.5)
    
    # Research outcomes
    add_box(7.5, 6, 1.2, 0.6, 'VH: Highest', colors['success'], 'white')
    add_box(8.5, 6, 1.2, 0.6, 'HI: High', colors['success'], 'white')
    add_box(9, 5.2, 1.2, 0.6, 'MD: Medium', colors['success'], 'white')
    add_box(8, 4.4, 1.2, 0.6, 'LO: Low', colors['success'], 'white')
    
    add_arrow(8, 7.1, 8.2, 6.5)
    
    # Final classification
    add_box(5, 3, 3, 0.8, 'Final Classification\n(e.g., L-CO-HI)', colors['primary'])
    
    # Arrows to final
    add_arrow(2, 5.5, 4, 3.5)
    add_arrow(5, 5.5, 5, 3.5)
    add_arrow(8, 5.5, 6, 3.5)
    
    # Strategic implications
    add_box(5, 1.5, 4, 0.8, 'Strategic Implications & Benchmarking\nComparisons', colors['neutral'])
    add_arrow(5, 2.6, 5, 2)
    
    plt.title('QS Institution Classification: Decision Process Flow\n(How Classifications Are Determined)', 
              fontsize=18, fontweight='bold', pad=30, color=colors['dark'])
    
    plt.tight_layout()
    plt.savefig('QS_Classification_Decision_Flowchart.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ Classification Decision Flowchart created: QS_Classification_Decision_Flowchart.png")

if __name__ == "__main__":
    print("Creating QS Institution Classifications Strategic Analysis...")
    print("=" * 65)
    
    create_strategic_implications_matrix()
    create_classification_decision_flowchart()
    
    print("=" * 65)
    print("Strategic analysis visualizations created successfully!")
    print("\nFiles generated:")
    print("1. QS_Classifications_Strategic_Implications_Matrix.png")
    print("2. QS_Classification_Decision_Flowchart.png")
    print("\nThese charts provide strategic insights for institutional planning and positioning.")
