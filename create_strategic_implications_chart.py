"""
QS Institution Classifications - Strategic Implications Visualization
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-27

This script creates strategic implications and practical considerations
charts for QS Institution Classifications.
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches
import warnings
warnings.filterwarnings('ignore')

def setup_professional_style():
    """Set up professional styling for all visualizations."""
    plt.style.use('default')
    
    colors = {
        'primary': '#2E86AB',
        'secondary': '#A23B72', 
        'accent': '#F18F01',
        'success': '#C73E1D',
        'neutral': '#6C757D',
        'light': '#F8F9FA',
        'dark': '#212529'
    }
    
    plt.rcParams.update({
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'Deja<PERSON>u Sans', 'Liberation Sans'],
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.titleweight': 'bold',
        'axes.labelsize': 14,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12,
        'legend.fontsize': 11,
        'figure.titlesize': 18,
        'figure.titleweight': 'bold',
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.grid': True,
        'grid.alpha': 0.3,
        'figure.facecolor': 'white',
        'axes.facecolor': 'white'
    })
    
    return colors

def create_strategic_implications_matrix():
    """Create strategic implications matrix for different classification combinations."""
    colors = setup_professional_style()
    
    fig, ax = plt.subplots(figsize=(16, 12))
    
    # Strategic implications data
    implications_data = {
        'Size + Focus': [
            'XL + FC', 'XL + CO', 'XL + FO', 'XL + SP',
            'L + FC', 'L + CO', 'L + FO', 'L + SP',
            'M + FC', 'M + CO', 'M + FO', 'M + SP',
            'S + FC', 'S + CO', 'S + FO', 'S + SP'
        ],
        'Research Threshold': [
            'Very High', 'High', 'High', 'Subject-based',
            'High', 'Moderate', 'Moderate', 'Subject-based',
            'Moderate', 'Low', 'Low', 'Subject-based',
            'Low', 'Very Low', 'Very Low', 'Subject-based'
        ],
        'Strategic Priority': [
            'Global Research Leadership', 'Comprehensive Excellence', 'Focused Excellence', 'Niche Leadership',
            'Regional Research Hub', 'Broad Academic Strength', 'Selective Excellence', 'Specialized Excellence',
            'Balanced Development', 'Academic Diversity', 'Strategic Focus', 'Deep Specialization',
            'Community Impact', 'Local Comprehensiveness', 'Targeted Strength', 'Unique Expertise'
        ],
        'Key Challenges': [
            'Maintaining breadth & depth', 'Resource allocation', 'Depth vs breadth', 'Market positioning',
            'Scaling research output', 'Quality consistency', 'Resource concentration', 'Visibility & impact',
            'Growth vs quality', 'Program sustainability', 'Competitive advantage', 'Market differentiation',
            'Resource constraints', 'Program viability', 'Faculty recruitment', 'Student attraction'
        ]
    }
    
    df = pd.DataFrame(implications_data)
    
    ax.axis('tight')
    ax.axis('off')
    
    # Create table
    table = ax.table(cellText=df.values,
                    colLabels=df.columns,
                    cellLoc='left',
                    loc='center',
                    colWidths=[0.15, 0.2, 0.35, 0.3])
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 1.8)
    
    # Header styling
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor(colors['primary'])
        table[(0, i)].set_text_props(weight='bold', color='white')
        table[(0, i)].set_height(0.12)
    
    # Row styling with size-based coloring
    size_colors = {
        'XL': colors['primary'],
        'L': colors['secondary'],
        'M': colors['accent'], 
        'S': colors['success']
    }
    
    for i in range(1, len(df) + 1):
        size_code = df.iloc[i-1]['Size + Focus'].split(' + ')[0]
        base_color = size_colors.get(size_code, colors['neutral'])
        
        for j in range(len(df.columns)):
            # Light background based on size
            table[(i, j)].set_facecolor(base_color)
            table[(i, j)].set_alpha(0.1)
            table[(i, j)].set_height(0.08)
            
            if j == 0:  # Size + Focus column
                table[(i, j)].set_text_props(weight='bold', color=base_color)
                table[(i, j)].set_alpha(0.3)
    
    plt.title('QS Institution Classifications: Strategic Implications Matrix\n(Classification Combinations and Strategic Considerations)', 
              fontsize=18, fontweight='bold', pad=40, color=colors['dark'])
    
    # Add legend
    legend_elements = [
        mpatches.Patch(color=colors['primary'], alpha=0.3, label='XL - Extra Large (>30k)'),
        mpatches.Patch(color=colors['secondary'], alpha=0.3, label='L - Large (≥12k)'),
        mpatches.Patch(color=colors['accent'], alpha=0.3, label='M - Medium (≥5k)'),
        mpatches.Patch(color=colors['success'], alpha=0.3, label='S - Small (<5k)')
    ]
    
    ax.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, -0.05), 
              ncol=4, frameon=False, fontsize=11)
    
    plt.tight_layout()
    plt.savefig('QS_Classifications_Strategic_Implications_Matrix.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ Strategic Implications Matrix created: QS_Classifications_Strategic_Implications_Matrix.png")

def create_classification_decision_flowchart():
    """Create clean decision flowchart for understanding classification process."""
    colors = setup_professional_style()

    fig, ax = plt.subplots(figsize=(16, 12))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')

    # Define flowchart elements with better positioning
    def add_box(x, y, width, height, text, color, text_color='white', fontsize=11):
        box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                           boxstyle="round,pad=0.15",
                           facecolor=color, edgecolor='black', linewidth=2)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=fontsize,
                color=text_color, weight='bold')

    def add_arrow(x1, y1, x2, y2, style='->', color=None):
        if color is None:
            color = colors['dark']
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle=style, lw=2.5, color=color))

    # Title and Start
    add_box(8, 10.5, 4, 1, 'QS Institution Classification Process', colors['primary'], fontsize=14)

    # Three main classification dimensions - horizontally aligned
    add_box(3, 8.5, 2.5, 1, 'STEP 1:\nSize Classification\n(Student FTE)', colors['secondary'])
    add_box(8, 8.5, 2.5, 1, 'STEP 2:\nSubject Range\n(Faculty Areas)', colors['accent'])
    add_box(13, 8.5, 2.5, 1, 'STEP 3:\nResearch Intensity\n(Scopus Documents)', colors['success'])

    # Arrows from start to three steps
    add_arrow(8, 10, 3, 9)
    add_arrow(8, 10, 8, 9)
    add_arrow(8, 10, 13, 9)

    # Size classification outcomes - vertical alignment
    add_box(1.5, 6.5, 1.8, 0.8, 'XL\n>30,000', colors['secondary'])
    add_box(2.5, 5.5, 1.8, 0.8, 'L\n≥12,000', colors['secondary'])
    add_box(3.5, 4.5, 1.8, 0.8, 'M\n≥5,000', colors['secondary'])
    add_box(4.5, 3.5, 1.8, 0.8, 'S\n<5,000', colors['secondary'])

    # Arrows from size classification to outcomes
    add_arrow(3, 8, 2.5, 7)

    # Subject range outcomes - vertical alignment
    add_box(6.5, 6.5, 1.8, 0.8, 'FC\n5+Medical', colors['accent'])
    add_box(7.5, 5.5, 1.8, 0.8, 'CO\n5 Areas', colors['accent'])
    add_box(8.5, 4.5, 1.8, 0.8, 'FO\n3-4 Areas', colors['accent'])
    add_box(9.5, 3.5, 1.8, 0.8, 'SP\n≤2 Areas', colors['accent'])

    # Arrows from subject range to outcomes
    add_arrow(8, 8, 8, 7)

    # Research intensity outcomes - vertical alignment
    add_box(11.5, 6.5, 1.8, 0.8, 'VH\nVery High', colors['success'])
    add_box(12.5, 5.5, 1.8, 0.8, 'HI\nHigh', colors['success'])
    add_box(13.5, 4.5, 1.8, 0.8, 'MD\nMedium', colors['success'])
    add_box(14.5, 3.5, 1.8, 0.8, 'LO\nLow', colors['success'])

    # Arrows from research intensity to outcomes
    add_arrow(13, 8, 13, 7)

    # Combination and final classification
    add_box(8, 2, 4, 1, 'FINAL CLASSIFICATION\nCombination\n(e.g., L-CO-HI)', colors['primary'], fontsize=12)

    # Clean arrows from all outcomes to final classification
    add_arrow(3, 3, 6.5, 2.5)
    add_arrow(8, 3, 8, 2.5)
    add_arrow(13, 3, 9.5, 2.5)

    # Strategic implications
    add_box(8, 0.5, 5, 0.8, 'Strategic Implications & Peer Benchmarking', colors['neutral'])
    add_arrow(8, 1.5, 8, 1)

    # Add legend boxes for clarity
    add_box(1, 10.5, 1.5, 0.6, 'SIZE', colors['secondary'], fontsize=10)
    add_box(8, 11.5, 1.5, 0.6, 'SUBJECT', colors['accent'], fontsize=10)
    add_box(15, 10.5, 1.5, 0.6, 'RESEARCH', colors['success'], fontsize=10)

    plt.title('QS Institution Classification: Clean Decision Process Flow\n(Systematic Classification Methodology)',
              fontsize=20, fontweight='bold', pad=40, color=colors['dark'])

    plt.tight_layout()
    plt.savefig('QS_Classification_Decision_Flowchart_Clean.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

    print("✓ Clean Classification Decision Flowchart created: QS_Classification_Decision_Flowchart_Clean.png")

def create_horizontal_classification_flowchart():
    """Create horizontal flowchart with minimal clutter and perfect alignment."""
    colors = setup_professional_style()

    fig, ax = plt.subplots(figsize=(18, 8))
    ax.set_xlim(0, 18)
    ax.set_ylim(0, 8)
    ax.axis('off')

    def add_rounded_box(x, y, width, height, text, color, text_color='white', fontsize=11):
        box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                           boxstyle="round,pad=0.2",
                           facecolor=color, edgecolor='black', linewidth=2)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=fontsize,
                color=text_color, weight='bold')

    def add_clean_arrow(x1, y1, x2, y2):
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle='->', lw=3, color=colors['dark']))

    # Main process flow - horizontal
    add_rounded_box(2, 6, 2.5, 1.2, 'Institution\nData Collection', colors['neutral'], fontsize=12)
    add_rounded_box(6, 6, 2.5, 1.2, 'Size Analysis\n(Student FTE)', colors['secondary'], fontsize=12)
    add_rounded_box(10, 6, 2.5, 1.2, 'Subject Range\n(Faculty Areas)', colors['accent'], fontsize=12)
    add_rounded_box(14, 6, 2.5, 1.2, 'Research Intensity\n(Scopus Docs)', colors['success'], fontsize=12)

    # Clean horizontal arrows
    add_clean_arrow(3.25, 6, 4.75, 6)
    add_clean_arrow(7.25, 6, 8.75, 6)
    add_clean_arrow(11.25, 6, 12.75, 6)

    # Classification outcomes - aligned below each step
    # Size outcomes
    add_rounded_box(5, 4, 1.5, 0.8, 'XL', colors['secondary'], fontsize=10)
    add_rounded_box(6, 3.2, 1.5, 0.8, 'L', colors['secondary'], fontsize=10)
    add_rounded_box(7, 2.4, 1.5, 0.8, 'M', colors['secondary'], fontsize=10)
    add_rounded_box(6, 1.6, 1.5, 0.8, 'S', colors['secondary'], fontsize=10)

    # Subject outcomes
    add_rounded_box(9, 4, 1.5, 0.8, 'FC', colors['accent'], fontsize=10)
    add_rounded_box(10, 3.2, 1.5, 0.8, 'CO', colors['accent'], fontsize=10)
    add_rounded_box(11, 2.4, 1.5, 0.8, 'FO', colors['accent'], fontsize=10)
    add_rounded_box(10, 1.6, 1.5, 0.8, 'SP', colors['accent'], fontsize=10)

    # Research outcomes
    add_rounded_box(13, 4, 1.5, 0.8, 'VH', colors['success'], fontsize=10)
    add_rounded_box(14, 3.2, 1.5, 0.8, 'HI', colors['success'], fontsize=10)
    add_rounded_box(15, 2.4, 1.5, 0.8, 'MD', colors['success'], fontsize=10)
    add_rounded_box(14, 1.6, 1.5, 0.8, 'LO', colors['success'], fontsize=10)

    # Arrows from main boxes to outcomes
    add_clean_arrow(6, 5.4, 6, 4.5)
    add_clean_arrow(10, 5.4, 10, 4.5)
    add_clean_arrow(14, 5.4, 14, 4.5)

    # Final result
    add_rounded_box(16.5, 6, 2.5, 1.2, 'Final Code\n(e.g., L-CO-HI)', colors['primary'], fontsize=12)
    add_clean_arrow(15.25, 6, 15.25, 6)

    # Add descriptive labels
    ax.text(6, 0.8, 'Size Categories:\nXL: >30k, L: ≥12k\nM: ≥5k, S: <5k',
            ha='center', va='center', fontsize=9, color=colors['secondary'],
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', edgecolor=colors['secondary']))

    ax.text(10, 0.8, 'Subject Focus:\nFC: 5+Med, CO: 5 Areas\nFO: 3-4, SP: ≤2',
            ha='center', va='center', fontsize=9, color=colors['accent'],
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', edgecolor=colors['accent']))

    ax.text(14, 0.8, 'Research Level:\nVH: Very High, HI: High\nMD: Medium, LO: Low',
            ha='center', va='center', fontsize=9, color=colors['success'],
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', edgecolor=colors['success']))

    plt.title('QS Institution Classification: Streamlined Process Flow\n(Clean Horizontal Layout)',
              fontsize=20, fontweight='bold', pad=30, color=colors['dark'])

    plt.tight_layout()
    plt.savefig('QS_Classification_Horizontal_Clean.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

    print("✓ Horizontal Clean Flowchart created: QS_Classification_Horizontal_Clean.png")

if __name__ == "__main__":
    print("Creating QS Institution Classifications Strategic Analysis...")
    print("=" * 65)
    
    create_strategic_implications_matrix()
    create_classification_decision_flowchart()
    create_horizontal_classification_flowchart()

    print("=" * 65)
    print("Strategic analysis visualizations created successfully!")
    print("\nFiles generated:")
    print("1. QS_Classifications_Strategic_Implications_Matrix.png")
    print("2. QS_Classification_Decision_Flowchart_Clean.png")
    print("3. QS_Classification_Horizontal_Clean.png")
    print("\nImproved flowcharts with:")
    print("• Clean, non-overlapping layout")
    print("• Properly aligned arrows")
    print("• Professional spacing and formatting")
    print("• Clear visual hierarchy")
    print("\nThese charts provide strategic insights for institutional planning and positioning.")
