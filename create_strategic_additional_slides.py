"""
Create Additional Strategic Slides for QS WUR 2026 Analysis
Author: Dr<PERSON>, Symbiosis International (Deemed University)

Based on comprehensive research report analysis, these slides address key strategic gaps.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import seaborn as sns
from matplotlib.patches import FancyBboxPatch

def create_india_success_story():
    """Create India's QS WUR success story and SIU's positioning."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'primary': '#1e40af',
        'secondary': '#3b82f6',
        'accent': '#10b981',
        'warning': '#f59e0b',
        'danger': '#ef4444',
        'india': '#ff6b35',
        'siu': '#1e40af'
    }
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
    fig.suptitle('India\'s QS WUR Success Story: SIU\'s Strategic Position', 
                 fontsize=20, fontweight='bold', y=0.96, color=colors['primary'])
    
    # 1. India's Growth Trajectory vs Other Countries
    ax1.set_title('India\'s Remarkable Growth in QS WUR', fontsize=16, fontweight='bold', color=colors['primary'])
    
    years = [2015, 2018, 2021, 2024, 2026]
    india_unis = [11, 18, 35, 46, 54]
    china_unis = [25, 40, 58, 71, 75]  # Estimated trajectory
    uk_unis = [78, 76, 84, 90, 91]    # Estimated trajectory
    
    ax1.plot(years, india_unis, marker='o', linewidth=4, markersize=8, 
            color=colors['india'], label='India', markerfacecolor=colors['india'])
    ax1.plot(years, china_unis, marker='s', linewidth=3, markersize=6, 
            color=colors['secondary'], label='China', alpha=0.7)
    ax1.plot(years, uk_unis, marker='^', linewidth=3, markersize=6, 
            color=colors['accent'], label='UK', alpha=0.7)
    
    # Highlight India's growth
    ax1.fill_between(years, india_unis, alpha=0.3, color=colors['india'])
    
    # Add growth annotations
    ax1.annotate('5x Growth!', xy=(2026, 54), xytext=(2023, 65),
                arrowprops=dict(arrowstyle='->', color=colors['india'], lw=2),
                fontsize=12, fontweight='bold', color=colors['india'])
    
    ax1.set_xlabel('Year', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Number of Ranked Universities', fontsize=12, fontweight='bold')
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 100)
    
    # 2. SIU Among Indian Universities
    ax2.set_title('SIU\'s Position Among Indian Universities', fontsize=16, fontweight='bold', color=colors['primary'])
    
    # Sample Indian universities with their approximate ranks
    indian_unis = ['IIT Delhi\n(123)', 'IIT Madras\n(180)', 'IISc\n(225)', 'IIT Kharagpur\n(271)', 
                   'IIT Kanpur\n(278)', 'Other IITs\n(300-500)', 'SIU\n(696)', 'Other Unis\n(700+)']
    ranks = [123, 180, 225, 271, 278, 400, 696, 800]
    
    # Create horizontal bar chart (inverted so lower rank = better)
    y_pos = np.arange(len(indian_unis))
    colors_bars = [colors['accent'] if 'IIT' in uni or 'IISc' in uni 
                   else colors['siu'] if 'SIU' in uni 
                   else colors['secondary'] for uni in indian_unis]
    
    bars = ax2.barh(y_pos, [1000-r for r in ranks], color=colors_bars, alpha=0.8)
    
    # Add rank labels
    for i, (bar, rank) in enumerate(zip(bars, ranks)):
        ax2.text(bar.get_width() + 10, bar.get_y() + bar.get_height()/2, 
                f'Rank {rank}', va='center', fontweight='bold', fontsize=10)
    
    ax2.set_yticks(y_pos)
    ax2.set_yticklabels(indian_unis, fontsize=10)
    ax2.set_xlabel('Ranking Position (Higher is Better)', fontsize=12, fontweight='bold')
    ax2.invert_yaxis()
    
    # 3. Peer Comparison - Multidisciplinary Universities
    ax3.set_title('SIU vs Global Multidisciplinary Universities', fontsize=16, fontweight='bold', color=colors['primary'])
    
    # Comparison with similar comprehensive universities
    peer_unis = ['NUS Singapore\n(8)', 'University of Toronto\n(25)', 'University of Sydney\n(18)', 
                'SIU Current\n(696)', 'SIU Target 2028\n(500)']
    peer_ranks = [8, 25, 18, 696, 500]
    peer_colors = [colors['accent'], colors['accent'], colors['accent'], colors['danger'], colors['warning']]
    
    bars_peer = ax3.bar(range(len(peer_unis)), peer_ranks, color=peer_colors, alpha=0.8)
    
    # Add value labels
    for bar, rank in zip(bars_peer, peer_ranks):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 20, 
                f'{rank}', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    ax3.set_xticks(range(len(peer_unis)))
    ax3.set_xticklabels(peer_unis, rotation=45, ha='right', fontsize=10)
    ax3.set_ylabel('QS WUR Rank (Lower is Better)', fontsize=12, fontweight='bold')
    ax3.invert_yaxis()
    ax3.grid(True, alpha=0.3)
    
    # 4. Strategic Opportunities
    ax4.text(0.5, 0.9, 'Strategic Opportunities for SIU', ha='center', va='center', 
            fontsize=16, fontweight='bold', transform=ax4.transAxes, color=colors['primary'])
    
    opportunities = [
        "Leverage India's rising global education profile",
        "Build on multidisciplinary comprehensive model", 
        "Target 500-600 rank range by 2028",
        "Strengthen subject-specific rankings first",
        "Enhance international research collaborations"
    ]
    
    for i, opportunity in enumerate(opportunities):
        y_pos = 0.7 - (i * 0.12)
        
        # Opportunity box
        opp_box = FancyBboxPatch((0.05, y_pos - 0.04), 0.9, 0.07,
                                boxstyle="round,pad=0.02", 
                                facecolor=colors['accent'], 
                                alpha=0.2, edgecolor=colors['accent'], linewidth=1)
        ax4.add_patch(opp_box)
        
        ax4.text(0.1, y_pos, f"• {opportunity}", ha='left', va='center', 
                fontsize=11, fontweight='bold', transform=ax4.transAxes, color='black')
    
    # Key insight
    ax4.text(0.5, 0.1, 'KEY INSIGHT: India\'s 5x growth creates favorable\nenvironment for SIU\'s ranking advancement', 
            ha='center', va='center', fontsize=12, fontweight='bold', 
            transform=ax4.transAxes, color=colors['india'],
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.8))
    
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_India_Success_SIU_Position.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_subject_rankings_strategy():
    """Create subject rankings strategy and SIU's competitive advantages."""
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    colors = {
        'primary': '#1e40af',
        'secondary': '#3b82f6',
        'accent': '#10b981',
        'warning': '#f59e0b',
        'danger': '#ef4444',
        'excellence': '#8b5cf6'
    }
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
    fig.suptitle('Subject Rankings Strategy: Leveraging SIU\'s Excellence Areas', 
                 fontsize=20, fontweight='bold', y=0.96, color=colors['primary'])
    
    # 1. SIU's Strong Subject Areas
    ax1.set_title('SIU\'s Top-Performing Subject Areas', fontsize=16, fontweight='bold', color=colors['primary'])
    
    subjects = ['Management\n& Business', 'Law &\nLegal Studies', 'Media &\nCommunications', 
               'Liberal Arts\n& Sciences', 'Computer\nScience', 'Medicine\n(New)']
    current_strength = [90, 85, 88, 82, 75, 60]  # Relative strength scores
    potential_ranking = [150, 200, 180, 250, 300, 400]  # Estimated QS subject ranking potential
    
    x = np.arange(len(subjects))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, current_strength, width, label='Current Strength Score', 
                   color=colors['accent'], alpha=0.8)
    
    # Add strength scores
    for bar, score in zip(bars1, current_strength):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{score}%', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    ax1.set_xlabel('Subject Areas', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Institutional Strength Score', fontsize=12, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(subjects, fontsize=10)
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 100)
    
    # 2. Subject to Overall Ranking Pathway
    ax2.set_title('Strategic Pathway: Subject Excellence → Overall Ranking', fontsize=16, fontweight='bold', color=colors['primary'])
    
    # Create pathway visualization
    pathway_steps = [
        {"step": "Subject Rankings\nTop 200", "y": 0.8, "color": colors['accent']},
        {"step": "Research Impact\nIncrease", "y": 0.6, "color": colors['secondary']},
        {"step": "International\nRecognition", "y": 0.4, "color": colors['warning']},
        {"step": "Overall Ranking\nImprovement", "y": 0.2, "color": colors['primary']}
    ]
    
    for i, step in enumerate(pathway_steps):
        # Step box
        step_box = FancyBboxPatch((0.2, step["y"] - 0.06), 0.6, 0.1,
                                 boxstyle="round,pad=0.02", 
                                 facecolor=step["color"], 
                                 alpha=0.8, edgecolor='black', linewidth=2)
        ax2.add_patch(step_box)
        
        ax2.text(0.5, step["y"], step["step"], ha='center', va='center', 
                fontsize=12, fontweight='bold', color='white', transform=ax2.transAxes)
        
        # Arrow to next step
        if i < len(pathway_steps) - 1:
            ax2.annotate('', xy=(0.5, step["y"] - 0.12), xytext=(0.5, step["y"] - 0.08),
                        arrowprops=dict(arrowstyle='->', lw=3, color='black'),
                        transform=ax2.transAxes)
    
    ax2.set_xlim(0, 1)
    ax2.set_ylim(0, 1)
    ax2.axis('off')
    
    # 3. Resource Allocation for Subject Excellence
    ax3.set_title('Recommended Resource Allocation by Subject', fontsize=16, fontweight='bold', color=colors['primary'])
    
    # Resource allocation pie chart
    subjects_short = ['Management', 'Law', 'Media', 'Liberal Arts', 'Computer Sci', 'Medicine']
    allocation = [25, 20, 20, 15, 10, 10]
    colors_pie = [colors['primary'], colors['accent'], colors['secondary'], 
                  colors['warning'], colors['excellence'], colors['danger']]
    
    wedges, texts, autotexts = ax3.pie(allocation, labels=subjects_short, autopct='%1.0f%%',
                                      colors=colors_pie, startangle=90)
    
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(11)
    
    # 4. Expected Impact Timeline
    ax4.set_title('Expected Impact Timeline (2025-2028)', fontsize=16, fontweight='bold', color=colors['primary'])
    
    timeline_years = ['2025', '2026', '2027', '2028']
    subject_rankings = [250, 200, 180, 150]  # Average subject ranking improvement
    overall_ranking = [696, 650, 580, 520]   # Overall ranking improvement
    
    ax4_twin = ax4.twinx()
    
    line1 = ax4.plot(timeline_years, subject_rankings, marker='o', linewidth=3, 
                    color=colors['accent'], label='Avg Subject Ranking', markersize=8)
    line2 = ax4_twin.plot(timeline_years, overall_ranking, marker='s', linewidth=3, 
                         color=colors['primary'], label='Overall Ranking', markersize=8)
    
    ax4.set_xlabel('Year', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Average Subject Ranking', fontsize=12, fontweight='bold', color=colors['accent'])
    ax4_twin.set_ylabel('Overall QS WUR Ranking', fontsize=12, fontweight='bold', color=colors['primary'])
    
    # Invert y-axes (lower rank is better)
    ax4.invert_yaxis()
    ax4_twin.invert_yaxis()
    
    # Add target annotations
    ax4.annotate('Target: Top 150\nin key subjects', xy=('2028', 150), xytext=('2026.5', 120),
                arrowprops=dict(arrowstyle='->', color=colors['accent'], lw=2),
                fontsize=10, fontweight='bold', color=colors['accent'])
    
    ax4_twin.annotate('Target: Top 500\noverall', xy=('2028', 520), xytext=('2026.5', 480),
                     arrowprops=dict(arrowstyle='->', color=colors['primary'], lw=2),
                     fontsize=10, fontweight='bold', color=colors['primary'])
    
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Subject_Rankings_Strategy.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_comprehensive_action_plan():
    """Create comprehensive action plan with timeline and resource requirements."""

    plt.style.use('seaborn-v0_8-whitegrid')

    colors = {
        'primary': '#1e40af',
        'secondary': '#3b82f6',
        'accent': '#10b981',
        'warning': '#f59e0b',
        'danger': '#ef4444',
        'success': '#059669'
    }

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('Comprehensive QS WUR Action Plan: 3-Year Strategic Roadmap (2025-2028)',
                 fontsize=20, fontweight='bold', y=0.96, color=colors['primary'])

    # 1. Timeline with Key Milestones
    ax1.set_title('Strategic Implementation Timeline', fontsize=16, fontweight='bold', color=colors['primary'])

    # Timeline data
    quarters = ['Q1 2025', 'Q2 2025', 'Q3 2025', 'Q4 2025', 'Q1 2026', 'Q2 2026',
               'Q3 2026', 'Q4 2026', 'Q1 2027', 'Q2 2027', 'Q3 2027', 'Q4 2027']

    milestones = [
        {'quarter': 'Q1 2025', 'action': 'Launch Employment Tracking', 'priority': 'High'},
        {'quarter': 'Q2 2025', 'action': 'International Partnership MOUs', 'priority': 'High'},
        {'quarter': 'Q3 2025', 'action': 'Sustainability Framework', 'priority': 'Medium'},
        {'quarter': 'Q4 2025', 'action': 'Research Infrastructure', 'priority': 'High'},
        {'quarter': 'Q2 2026', 'action': 'Subject Rankings Push', 'priority': 'High'},
        {'quarter': 'Q4 2026', 'action': 'International Faculty Drive', 'priority': 'Medium'},
        {'quarter': 'Q2 2027', 'action': 'Citation Impact Program', 'priority': 'High'},
        {'quarter': 'Q4 2027', 'action': 'Comprehensive Review', 'priority': 'Medium'}
    ]

    # Create timeline
    y_positions = {quarter: i for i, quarter in enumerate(quarters)}

    for milestone in milestones:
        y_pos = y_positions[milestone['quarter']]
        color = colors['danger'] if milestone['priority'] == 'High' else colors['warning']

        # Milestone marker
        ax1.scatter(0.5, y_pos, s=200, c=color, alpha=0.8, edgecolors='black', linewidth=2)

        # Milestone text
        ax1.text(0.6, y_pos, milestone['action'], va='center', fontsize=10, fontweight='bold')

    ax1.set_yticks(range(len(quarters)))
    ax1.set_yticklabels(quarters, fontsize=10)
    ax1.set_xlim(0, 1)
    ax1.set_title('Key Implementation Milestones', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3, axis='y')

    # 2. Resource Requirements by Initiative
    ax2.set_title('Resource Requirements (Annual Investment)', fontsize=16, fontweight='bold', color=colors['primary'])

    initiatives = ['Employment\nTracking', 'International\nPartnerships', 'Research\nInfrastructure',
                  'Sustainability\nPrograms', 'Faculty\nDevelopment']
    investments = [15, 25, 35, 10, 20]  # In lakhs
    roi_timeline = [12, 18, 24, 36, 18]  # Months to see impact

    bars = ax2.bar(initiatives, investments, color=[colors['accent'], colors['primary'],
                                                   colors['secondary'], colors['warning'], colors['success']],
                  alpha=0.8, edgecolor='black', linewidth=1)

    # Add investment labels
    for bar, investment, roi in zip(bars, investments, roi_timeline):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'₹{investment}L\n({roi}m ROI)', ha='center', va='bottom',
                fontweight='bold', fontsize=9)

    ax2.set_ylabel('Annual Investment (₹ Lakhs)', fontsize=12, fontweight='bold')
    ax2.set_xlabel('Strategic Initiatives', fontsize=12, fontweight='bold')
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.set_ylim(0, 45)

    # 3. Expected Ranking Trajectory
    ax3.set_title('Expected QS WUR Ranking Improvement', fontsize=16, fontweight='bold', color=colors['primary'])

    years = [2025, 2026, 2027, 2028]
    current_trajectory = [696, 680, 650, 620]  # Without strategic intervention
    strategic_trajectory = [696, 650, 580, 520]  # With strategic intervention
    target_trajectory = [696, 630, 550, 480]    # Optimistic scenario

    ax3.plot(years, current_trajectory, marker='o', linewidth=3, color=colors['danger'],
            label='Current Trajectory', linestyle='--', alpha=0.7)
    ax3.plot(years, strategic_trajectory, marker='s', linewidth=4, color=colors['primary'],
            label='Strategic Plan', markersize=8)
    ax3.plot(years, target_trajectory, marker='^', linewidth=3, color=colors['accent'],
            label='Optimistic Target', linestyle=':', alpha=0.8)

    # Fill area between current and strategic
    ax3.fill_between(years, current_trajectory, strategic_trajectory,
                    alpha=0.3, color=colors['accent'], label='Strategic Advantage')

    ax3.set_xlabel('Year', fontsize=12, fontweight='bold')
    ax3.set_ylabel('QS WUR Global Ranking', fontsize=12, fontweight='bold')
    ax3.legend(fontsize=11)
    ax3.grid(True, alpha=0.3)
    ax3.invert_yaxis()  # Lower rank is better

    # Add target annotation
    ax3.annotate('Target: Top 500\nby 2028', xy=(2028, 520), xytext=(2026.5, 480),
                arrowprops=dict(arrowstyle='->', color=colors['primary'], lw=2),
                fontsize=11, fontweight='bold', color=colors['primary'],
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))

    # 4. Risk Assessment and Mitigation
    ax4.text(0.5, 0.9, 'Risk Assessment & Mitigation Strategies', ha='center', va='center',
            fontsize=16, fontweight='bold', transform=ax4.transAxes, color=colors['primary'])

    risks = [
        {'risk': 'Increased Global Competition', 'probability': 'High', 'impact': 'Medium',
         'mitigation': 'Focus on differentiation through multidisciplinary excellence'},
        {'risk': 'Resource Constraints', 'probability': 'Medium', 'impact': 'High',
         'mitigation': 'Phased implementation with priority-based allocation'},
        {'risk': 'Faculty Retention', 'probability': 'Medium', 'impact': 'Medium',
         'mitigation': 'Enhanced career development and research support'},
        {'risk': 'Methodology Changes', 'probability': 'Low', 'impact': 'High',
         'mitigation': 'Balanced approach across all indicators'}
    ]

    for i, risk in enumerate(risks):
        y_pos = 0.7 - (i * 0.15)

        # Risk level color
        risk_color = colors['danger'] if risk['impact'] == 'High' else colors['warning']

        # Risk box
        risk_box = FancyBboxPatch((0.02, y_pos - 0.05), 0.96, 0.08,
                                 boxstyle="round,pad=0.02",
                                 facecolor=risk_color,
                                 alpha=0.1, edgecolor=risk_color, linewidth=1)
        ax4.add_patch(risk_box)

        # Risk text
        ax4.text(0.05, y_pos, f"Risk: {risk['risk']}", ha='left', va='center',
                fontsize=10, fontweight='bold', transform=ax4.transAxes, color='black')
        ax4.text(0.05, y_pos - 0.025, f"Mitigation: {risk['mitigation']}", ha='left', va='center',
                fontsize=9, fontweight='normal', transform=ax4.transAxes, color='black')

    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')

    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Comprehensive_Action_Plan.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Creating additional strategic slides for QS WUR 2026 analysis...")

    # Create India success story and SIU positioning
    create_india_success_story()
    print("✓ India success story created: QS_WUR_2026_India_Success_SIU_Position.png")

    # Create subject rankings strategy
    create_subject_rankings_strategy()
    print("✓ Subject rankings strategy created: QS_WUR_2026_Subject_Rankings_Strategy.png")

    # Create comprehensive action plan
    create_comprehensive_action_plan()
    print("✓ Comprehensive action plan created: QS_WUR_2026_Comprehensive_Action_Plan.png")

    print("\nAdditional strategic visualizations created:")
    print("• India's QS WUR success story and SIU's strategic position")
    print("• Subject rankings strategy leveraging SIU's excellence areas")
    print("• Comprehensive 3-year action plan with timeline and resources")
    print("• Risk assessment and mitigation strategies")
    print("• Expected ranking trajectory with strategic intervention")
    print("\n🎯 Complete strategic package ready for Directors Meeting!")
