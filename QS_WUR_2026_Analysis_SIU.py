"""
QS WUR 2026 Analysis - PowerPoint Generation and Visualization
Author: Dr<PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-23

This script generates comprehensive PowerPoint slides and supporting visualizations
for the QS WUR 2026 analysis presentation at SIU Directors Meeting.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
import numpy as np
import pandas as pd
from matplotlib.patches import FancyBboxPatch
import matplotlib.gridspec as gridspec
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import io
import base64

# Set up visualization styling
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Color scheme for SIU branding
SIU_COLORS = {
    'primary': '#1e40af',
    'secondary': '#3b82f6', 
    'accent': '#10b981',
    'warning': '#f59e0b',
    'danger': '#ef4444',
    'light': '#f8fafc',
    'dark': '#1f2937'
}

def setup_visualization_style():
    """Set global styling for all visualizations."""
    plt.rcParams['font.family'] = 'Arial'
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['axes.titleweight'] = 'bold'
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['figure.figsize'] = (16, 10)
    plt.rcParams['figure.dpi'] = 300

def create_methodology_comparison_chart():
    """Create comprehensive methodology comparison visualization."""
    setup_visualization_style()
    
    # Data for methodology changes
    indicators = ['Academic\nReputation', 'Faculty Student\nRatio', 'Employer\nReputation', 
                 'Employment\nOutcomes', 'International\nResearch Network', 'Sustainability',
                 'Citations per\nFaculty', 'International\nFaculty Ratio', 'International\nStudent Ratio']
    
    weights_2024 = [40, 20, 10, 0, 0, 0, 20, 5, 5]
    weights_2026 = [30, 10, 15, 5, 5, 5, 20, 5, 5]
    changes = [w2026 - w2024 for w2024, w2026 in zip(weights_2024, weights_2026)]
    
    # Create figure with subplots
    fig = plt.figure(figsize=(20, 12))
    gs = gridspec.GridSpec(2, 2, height_ratios=[3, 2], width_ratios=[3, 2])
    
    # Main comparison chart
    ax1 = fig.add_subplot(gs[0, :])
    
    x = np.arange(len(indicators))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, weights_2024, width, label='2024 Weights', 
                   color=SIU_COLORS['secondary'], alpha=0.8)
    bars2 = ax1.bar(x + width/2, weights_2026, width, label='2025/26 Weights', 
                   color=SIU_COLORS['primary'], alpha=0.8)
    
    # Add change indicators
    for i, change in enumerate(changes):
        if change > 0:
            ax1.annotate(f'+{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 1),
                        ha='center', va='bottom', fontweight='bold', color=SIU_COLORS['accent'],
                        fontsize=11)
        elif change < 0:
            ax1.annotate(f'{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 1),
                        ha='center', va='bottom', fontweight='bold', color=SIU_COLORS['danger'],
                        fontsize=11)
    
    ax1.set_xlabel('QS WUR Indicators', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Weight Percentage (%)', fontsize=14, fontweight='bold')
    ax1.set_title('QS World University Rankings 2026: Methodology Changes\nShift from Input-Based to Outcome-Based Evaluation', 
                 fontsize=18, fontweight='bold', pad=20)
    ax1.set_xticks(x)
    ax1.set_xticklabels(indicators, rotation=45, ha='right')
    ax1.legend(fontsize=12, loc='upper right')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 45)
    
    # Add value labels on bars
    for bar in bars1:
        height = bar.get_height()
        if height > 0:
            ax1.annotate(f'{height}%', xy=(bar.get_x() + bar.get_width()/2, height),
                        xytext=(0, 3), textcoords="offset points", ha='center', va='bottom',
                        fontsize=10, fontweight='bold')
    
    for bar in bars2:
        height = bar.get_height()
        if height > 0:
            ax1.annotate(f'{height}%', xy=(bar.get_x() + bar.get_width()/2, height),
                        xytext=(0, 3), textcoords="offset points", ha='center', va='bottom',
                        fontsize=10, fontweight='bold')
    
    # India's performance trends
    ax2 = fig.add_subplot(gs[1, 0])
    
    years = ['2015', '2020', '2025', '2026']
    indian_unis = [11, 25, 46, 54]
    
    ax2.plot(years, indian_unis, marker='o', linewidth=3, markersize=8, 
            color=SIU_COLORS['accent'], markerfacecolor=SIU_COLORS['primary'])
    ax2.fill_between(years, indian_unis, alpha=0.3, color=SIU_COLORS['accent'])
    
    for i, v in enumerate(indian_unis):
        ax2.annotate(f'{v}', xy=(i, v), xytext=(0, 10), textcoords="offset points",
                    ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    ax2.set_title('India\'s Rising Trajectory in QS WUR\n5x Growth Since 2015', 
                 fontsize=14, fontweight='bold')
    ax2.set_ylabel('Number of Ranked Universities', fontsize=12, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # SIU positioning
    ax3 = fig.add_subplot(gs[1, 1])
    
    categories = ['Research\nOutput', 'Multi-\ndisciplinary', 'International\nStudents', 'Industry\nAlignment']
    siu_scores = [85, 90, 75, 88]  # Relative strength scores
    
    bars = ax3.bar(categories, siu_scores, color=[SIU_COLORS['accent'], SIU_COLORS['primary'], 
                                                 SIU_COLORS['warning'], SIU_COLORS['secondary']])
    
    ax3.set_title('SIU\'s Strategic Strengths\nPositioning for QS WUR Growth', 
                 fontsize=14, fontweight='bold')
    ax3.set_ylabel('Relative Strength Score', fontsize=12, fontweight='bold')
    ax3.set_ylim(0, 100)
    
    for bar, score in zip(bars, siu_scores):
        ax3.annotate(f'{score}%', xy=(bar.get_x() + bar.get_width()/2, score),
                    xytext=(0, 3), textcoords="offset points", ha='center', va='bottom',
                    fontweight='bold', fontsize=11)
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Methodology_Analysis.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()

def create_strategic_impact_visualization():
    """Create strategic impact and recommendations visualization."""
    setup_visualization_style()
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    
    # 1. Impact Assessment Matrix
    impact_areas = ['Employment\nOutcomes', 'International\nResearch', 'Sustainability', 
                   'Academic\nReputation', 'Faculty\nRatio']
    current_performance = [65, 70, 45, 80, 85]  # SIU's estimated current performance
    improvement_potential = [90, 85, 80, 85, 75]  # Potential after strategic actions
    
    x = np.arange(len(impact_areas))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, current_performance, width, label='Current Performance', 
                   color=SIU_COLORS['warning'], alpha=0.7)
    bars2 = ax1.bar(x + width/2, improvement_potential, width, label='Improvement Potential', 
                   color=SIU_COLORS['accent'], alpha=0.8)
    
    ax1.set_title('SIU Performance vs. Improvement Potential\nKey QS WUR Indicators', 
                 fontsize=16, fontweight='bold')
    ax1.set_ylabel('Performance Score (%)', fontsize=12, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(impact_areas)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 100)
    
    # Add improvement arrows
    for i, (current, potential) in enumerate(zip(current_performance, improvement_potential)):
        if potential > current:
            ax1.annotate('', xy=(i, potential), xytext=(i, current + 5),
                        arrowprops=dict(arrowstyle='->', color=SIU_COLORS['accent'], lw=2))
    
    # 2. Competitive Positioning
    universities = ['IIT Delhi\n(123)', 'IIT Madras\n(180)', 'SIU\n(696)', 'Target\n(500)', 'Target\n(400)']
    ranks = [123, 180, 696, 500, 400]
    colors = [SIU_COLORS['accent'], SIU_COLORS['accent'], SIU_COLORS['danger'], 
              SIU_COLORS['warning'], SIU_COLORS['primary']]
    
    bars = ax2.barh(universities, [1000-r for r in ranks], color=colors, alpha=0.8)
    ax2.set_title('SIU Competitive Positioning\nQS WUR 2026 Rankings', 
                 fontsize=16, fontweight='bold')
    ax2.set_xlabel('Ranking Position (Higher is Better)', fontsize=12, fontweight='bold')
    
    # Add rank labels
    for i, (bar, rank) in enumerate(zip(bars, ranks)):
        ax2.text(bar.get_width() + 10, bar.get_y() + bar.get_height()/2, 
                f'Rank {rank}', va='center', fontweight='bold')
    
    # 3. Strategic Action Timeline
    actions = ['Employment\nTracking', 'International\nPartnerships', 'Sustainability\nPrograms', 
              'Research\nCollaboration', 'Quality\nExpansion']
    timeline = [6, 12, 18, 24, 36]  # months
    impact_level = [85, 90, 75, 95, 80]  # expected impact
    
    scatter = ax3.scatter(timeline, impact_level, s=[200, 250, 180, 300, 220], 
                         c=[SIU_COLORS['accent'], SIU_COLORS['primary'], SIU_COLORS['warning'],
                            SIU_COLORS['secondary'], SIU_COLORS['danger']], alpha=0.7)
    
    for i, action in enumerate(actions):
        ax3.annotate(action, (timeline[i], impact_level[i]), xytext=(5, 5), 
                    textcoords='offset points', fontsize=10, fontweight='bold')
    
    ax3.set_title('Strategic Action Timeline\nImplementation Roadmap', 
                 fontsize=16, fontweight='bold')
    ax3.set_xlabel('Timeline (Months)', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Expected Impact Level', fontsize=12, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.set_xlim(0, 40)
    ax3.set_ylim(70, 100)
    
    # 4. Resource Allocation Pie Chart
    resources = ['Research Enhancement', 'International Partnerships', 'Career Services', 
                'Sustainability Initiatives', 'Infrastructure Development']
    allocation = [30, 25, 20, 15, 10]
    colors_pie = [SIU_COLORS['primary'], SIU_COLORS['accent'], SIU_COLORS['secondary'],
                  SIU_COLORS['warning'], SIU_COLORS['danger']]
    
    wedges, texts, autotexts = ax4.pie(allocation, labels=resources, autopct='%1.1f%%',
                                      colors=colors_pie, startangle=90)
    ax4.set_title('Recommended Resource Allocation\nStrategic Investment Priorities', 
                 fontsize=16, fontweight='bold')
    
    # Enhance pie chart text
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(11)
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Strategic_Impact.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_powerpoint_presentation():
    """Create comprehensive PowerPoint presentation."""
    
    # Create presentation
    prs = Presentation()
    
    # Set slide dimensions (16:9 aspect ratio)
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)
    
    # Define colors
    blue_color = RGBColor(30, 64, 175)  # SIU primary blue
    accent_color = RGBColor(59, 130, 246)  # SIU secondary blue
    
    # Slide 1: Title Slide
    slide1_layout = prs.slide_layouts[0]  # Title slide layout
    slide1 = prs.slides.add_slide(slide1_layout)
    
    title1 = slide1.shapes.title
    subtitle1 = slide1.placeholders[1]
    
    title1.text = "QS World University Rankings 2026"
    title1.text_frame.paragraphs[0].font.size = Pt(44)
    title1.text_frame.paragraphs[0].font.color.rgb = blue_color
    title1.text_frame.paragraphs[0].font.bold = True
    
    subtitle1.text = "Strategic Analysis for Symbiosis International University\n\nPresented by: Dr. Dharmendra Pandey\nDeputy Director - QMB & Head - QA\nSymbiosis International (Deemed University)"
    for paragraph in subtitle1.text_frame.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = RGBColor(107, 114, 128)
    
    # Slide 2: Introduction to QS WUR 2026
    slide2_layout = prs.slide_layouts[1]  # Title and content layout
    slide2 = prs.slides.add_slide(slide2_layout)
    
    title2 = slide2.shapes.title
    title2.text = "Introduction to QS World University Rankings 2026"
    title2.text_frame.paragraphs[0].font.size = Pt(32)
    title2.text_frame.paragraphs[0].font.color.rgb = blue_color
    title2.text_frame.paragraphs[0].font.bold = True
    
    content2 = slide2.placeholders[1]
    content2.text = """What is QS WUR?
• Annual global university ranking by Quacquarelli Symonds
• Evaluates 1,500+ universities across 105 higher education systems
• Uses sophisticated multi-layered methodology with "lenses," "indicators," and "metrics"
• Highly regarded benchmark for institutional reputation and student decision-making

India's Remarkable Trajectory
• 54 Indian universities ranked in 2026 (vs. 11 in 2015) - 5x growth
• 4th most represented country globally
• 8 new Indian entrants - highest globally
• 48% of institutions improved their positions

SIU's Current Position
• Rank 696 globally in QS WUR 2026 (slight decline from 641-650 in 2025)
• Among top 1000 universities worldwide
• Reinforces Pune's status as significant educational hub alongside SPPU"""
    
    for paragraph in content2.text_frame.paragraphs:
        paragraph.font.size = Pt(16)
        paragraph.level = 0 if paragraph.text.endswith(':') else 1
        if paragraph.level == 0:
            paragraph.font.bold = True
            paragraph.font.color.rgb = blue_color
    
    return prs

# Generate visualizations and PowerPoint
if __name__ == "__main__":
    print("Generating QS WUR 2026 Analysis Visualizations...")
    
    # Create methodology comparison chart
    create_methodology_comparison_chart()
    print("✓ Methodology comparison chart created: QS_WUR_2026_Methodology_Analysis.png")
    
    # Create strategic impact visualization
    create_strategic_impact_visualization()
    print("✓ Strategic impact visualization created: QS_WUR_2026_Strategic_Impact.png")
    
    # Create PowerPoint presentation
    prs = create_powerpoint_presentation()
    
    print("✓ PowerPoint presentation structure created")
    print("\nAll visualizations and presentation components generated successfully!")
    print("\nFiles created:")
    print("1. QS_WUR_2026_Methodology_Analysis.png - Comprehensive methodology analysis")
    print("2. QS_WUR_2026_Strategic_Impact.png - Strategic impact and recommendations")
    print("3. PowerPoint presentation structure (to be completed)")
